openapi: 3.0.0
paths:
  /:
    get:
      operationId: App<PERSON><PERSON><PERSON>er_getHello
      parameters: []
      responses:
        '200':
          description: ''
      tags:
        - App
  /user:
    post:
      operationId: UserController_create
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserDto'
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  email:
                    type: string
                  role:
                    type: string
                    enum:
                      - teacher
                      - admin
        '400':
          description: Bad request
      security: &ref_0
        - bearer: []
      summary: Create a new user
      tags: &ref_1
        - User
    get:
      operationId: UserController_findAll
      parameters:
        - name: schoolId
          required: false
          in: query
          description: Filter users by school ID (UUID format)
          schema:
            example: 123e4567-e89b-12d3-a456-************
            type: string
        - name: role
          required: false
          in: query
          description: Filter users by role
          schema:
            $ref: '#/components/schemas/EUserRole'
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: string
                    name:
                      type: string
                    email:
                      type: string
                    role:
                      type: string
                      enum:
                        - teacher
                        - admin
                        - student
                        - school_manager
                    schoolId:
                      type: string
                      nullable: true
      security: *ref_0
      summary: Get all users with optional filtering
      tags: *ref_1
  /user/me:
    get:
      operationId: UserController_me
      parameters: []
      responses:
        '200':
          description: Current user profile retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  email:
                    type: string
                  role:
                    type: string
                    enum:
                      - teacher
                      - admin
        '401':
          description: Unauthorized
      security: *ref_0
      summary: Get current user profile
      tags: *ref_1
  /user/{id}:
    get:
      operationId: UserController_findOne
      parameters:
        - name: id
          required: true
          in: path
          description: User ID
          schema:
            type: string
      responses:
        '200':
          description: User retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  email:
                    type: string
                  role:
                    type: string
                    enum:
                      - teacher
                      - admin
        '404':
          description: User not found
      security: *ref_0
      summary: Get user by ID
      tags: *ref_1
    patch:
      operationId: UserController_update
      parameters:
        - name: id
          required: true
          in: path
          description: User ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserDto'
      responses:
        '200':
          description: User updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  email:
                    type: string
                  role:
                    type: string
                    enum:
                      - teacher
                      - admin
                      - student
                      - school_manager
        '400':
          description: Bad request
        '404':
          description: User not found
      security: *ref_0
      summary: Update a user
      tags: *ref_1
  /auth/sign-in:
    post:
      operationId: AuthController_signIn
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SignInDto'
      responses:
        '200':
          description: User signed in successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  accessToken:
                    type: string
                  user:
                    type: object
                    properties:
                      id:
                        type: string
                      name:
                        type: string
                      email:
                        type: string
        '401':
          description: Unauthorized
      summary: Sign in with email and password
      tags: &ref_2
        - Auth
  /auth/sign-up:
    post:
      operationId: AuthController_signUp
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SignUpDto'
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  email:
                    type: string
        '400':
          description: Bad request
      summary: Register a new user
      tags: *ref_2
  /schools:
    post:
      operationId: SchoolController_create
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSchoolDto'
      responses:
        '201':
          description: The school has been successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/School'
        '403':
          description: Forbidden.
      security: &ref_3
        - bearer: []
      summary: Create a new school
      tags: &ref_4
        - Schools
    get:
      operationId: SchoolController_findAll
      parameters: []
      responses:
        '200':
          description: Return all schools.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/School'
      security: *ref_3
      summary: Get all schools
      tags: *ref_4
  /schools/{id}:
    get:
      operationId: SchoolController_findOne
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Return the school.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/School'
        '404':
          description: School not found.
      security: *ref_3
      summary: Get a school by id
      tags: *ref_4
    patch:
      operationId: SchoolController_update
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSchoolDto'
      responses:
        '200':
          description: The school has been successfully updated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/School'
        '403':
          description: Forbidden.
        '404':
          description: School not found.
      security: *ref_3
      summary: Update a school
      tags: *ref_4
    delete:
      operationId: SchoolController_remove
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: The school has been successfully deleted.
        '403':
          description: Forbidden.
        '404':
          description: School not found.
      security: *ref_3
      summary: Delete a school
      tags: *ref_4
  /schools/examination-format:
    post:
      operationId: SchoolController_uploadExaminationFormat
      parameters: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: PDF file containing the examination format
                schoolId:
                  type: string
                  description: School ID for which the examination format applies
      responses:
        '201':
          description: The examination format has been successfully uploaded.
        '400':
          description: Bad request.
        '403':
          description: Forbidden.
      security: *ref_3
      summary: Upload a PDF file defining the examination format for a specific school
      tags: *ref_4
  /schools/{schoolId}/examination-format:
    get:
      operationId: SchoolController_getExaminationFormat
      parameters:
        - name: schoolId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Return the examination format text.
          content:
            application/json:
              schema:
                type: string
        '403':
          description: Forbidden.
        '404':
          description: School or examination format not found.
      security: *ref_3
      summary: Get the examination format for a school
      tags: *ref_4
    delete:
      operationId: SchoolController_deleteExaminationFormat
      parameters:
        - name: schoolId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: The examination format has been successfully deleted.
        '403':
          description: Forbidden.
        '404':
          description: School or examination format not found.
      security: *ref_3
      summary: Delete the examination format for a school
      tags: *ref_4
  /schools/{schoolId}/narrative-structure/extract:
    post:
      operationId: SchoolController_extractNarrativeStructure
      parameters:
        - name: schoolId
          required: true
          in: path
          schema:
            type: string
      responses:
        '201':
          description: Narrative structure extracted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExtractNarrativeStructureResponseDto'
        '400':
          description: Bad request - No examination formats found or extraction failed
        '403':
          description: Forbidden - Admin access required
        '404':
          description: School not found
      security: *ref_3
      summary: >-
        Extract narrative structure from examination formats for a specific
        school
      tags: *ref_4
  /schools/narrative-structure/extract-all:
    post:
      operationId: SchoolController_extractAllNarrativeStructures
      parameters: []
      responses:
        '201':
          description: Bulk narrative structure extraction completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkExtractNarrativeStructureResponseDto'
        '403':
          description: Forbidden - Admin access required
      security: *ref_3
      summary: Extract narrative structures for all schools with examination formats
      tags: *ref_4
  /schools/{schoolId}/narrative-structure:
    get:
      operationId: SchoolController_getNarrativeStructure
      parameters:
        - name: schoolId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Return the narrative structure
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NarrativeStructureResponseDto'
        '403':
          description: Forbidden - Admin access required
        '404':
          description: School or narrative structure not found
      security: *ref_3
      summary: Get the narrative structure for a school
      tags: *ref_4
    delete:
      operationId: SchoolController_deleteNarrativeStructure
      parameters:
        - name: schoolId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Narrative structure deleted successfully
        '403':
          description: Forbidden - Admin access required
        '404':
          description: School or narrative structure not found
      security: *ref_3
      summary: Delete the narrative structure for a school
      tags: *ref_4
  /brands:
    post:
      operationId: BrandController_create
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBrandDto'
      responses:
        '201':
          description: The brand has been successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Brand'
        '403':
          description: Forbidden.
      security: &ref_5
        - bearer: []
      summary: Create a new brand
      tags: &ref_6
        - Brands
    get:
      operationId: BrandController_findAll
      parameters: []
      responses:
        '200':
          description: Return all brands.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Brand'
      security: *ref_5
      summary: Get all brands
      tags: *ref_6
  /brands/{id}:
    get:
      operationId: BrandController_findOne
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Return the brand.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Brand'
        '404':
          description: Brand not found.
      security: *ref_5
      summary: Get a brand by id
      tags: *ref_6
    patch:
      operationId: BrandController_update
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateBrandDto'
      responses:
        '200':
          description: The brand has been successfully updated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Brand'
        '403':
          description: Forbidden.
        '404':
          description: Brand not found.
      security: *ref_5
      summary: Update a brand
      tags: *ref_6
    delete:
      operationId: BrandController_remove
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: The brand has been successfully deleted.
        '403':
          description: Forbidden.
        '404':
          description: Brand not found.
      security: *ref_5
      summary: Delete a brand
      tags: *ref_6
  /documents:
    get:
      operationId: DocumentsController_queryDocuments
      parameters:
        - name: query
          required: true
          in: query
          description: Search term to query documents
          schema:
            type: string
        - name: category
          required: false
          in: query
          description: Category to filter documents (optional)
          schema:
            type: string
      responses:
        '200':
          description: Documents retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: number
                    example: 200
                  message:
                    type: string
                    example: Documents retrieved successfully
                  data:
                    type: string
                    example: Document content...
        '400':
          description: Bad request
      summary: Query documents based on search term and optional category
      tags: &ref_7
        - Documents
  /documents/upload:
    post:
      operationId: DocumentsController_uploadDocuments
      parameters: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                title:
                  type: string
                  description: Title of the document(s)
                description:
                  type: string
                  description: Description of the document(s)
                category:
                  type: string
                  description: Category of the document(s)
      responses:
        '200':
          description: Documents uploaded successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: number
                    example: 200
                  message:
                    type: string
                    example: Documents uploaded successfully
                  data:
                    type: object
                    properties:
                      count:
                        type: number
                        example: 2
                      results:
                        type: array
                        items:
                          type: object
                          properties:
                            documentId:
                              type: string
                            title:
                              type: string
                            description:
                              type: string
                            fileName:
                              type: string
                            mimeType:
                              type: string
                            uploadedAt:
                              type: string
                              format: date-time
                            category:
                              type: string
        '400':
          description: Bad request
      summary: Upload documents for processing
      tags: *ref_7
  /prompt/exercise:
    post:
      operationId: PromptController_genExercise
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenPromptDto'
      responses:
        '201':
          description: Exercise generated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        question:
                          type: string
                        options:
                          type: array
                          items:
                            type: string
                        answer:
                          type: string
                        explanation:
                          type: string
                        image:
                          type: string
        '400':
          description: Bad request
      summary: Generate exercise based on provided content and requirements
      tags:
        - Prompt
  /ai-service-logs:
    get:
      operationId: AiServiceLogController_getAllLogs
      parameters:
        - name: status
          required: false
          in: query
          schema:
            enum:
              - success
              - error
            type: string
        - name: worksheetId
          required: false
          in: query
          schema:
            type: string
        - name: startDate
          required: false
          in: query
          schema:
            format: date-time
            type: string
        - name: endDate
          required: false
          in: query
          schema:
            format: date-time
            type: string
      responses:
        '200':
          description: Returns all AI service logs matching the filter criteria
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AiServiceLog'
      summary: Get all AI service logs with optional filtering
      tags: &ref_8
        - AI Service Logs
  /ai-service-logs/worksheet/{worksheetId}:
    get:
      operationId: AiServiceLogController_getLogsByWorksheetId
      parameters:
        - name: worksheetId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Returns AI service logs for the specified worksheet
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AiServiceLog'
      summary: Get AI service logs by worksheet ID
      tags: *ref_8
  /ai-service-logs/batch/{batchId}:
    get:
      operationId: AiServiceLogController_getLogByBatchId
      parameters:
        - name: batchId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: >-
            Returns the AI service log for the specified batch or null if not
            found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AiServiceLog'
      summary: Get AI service log by batch ID
      tags: *ref_8
  /files/upload:
    post:
      operationId: FilesController_uploadFile
      parameters: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                description:
                  type: string
                category:
                  type: string
                tags:
                  type: array
                  items:
                    type: string
      responses:
        '201':
          description: ''
      summary: Upload a single file
      tags: &ref_9
        - files
  /files/upload/multiple:
    post:
      operationId: FilesController_uploadFiles
      parameters: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                description:
                  type: string
                category:
                  type: string
                tags:
                  type: array
                  items:
                    type: string
      responses:
        '201':
          description: ''
      summary: Upload multiple files
      tags: *ref_9
  /files:
    get:
      operationId: FilesController_getFiles
      parameters:
        - name: page
          required: false
          in: query
          description: Page number
          schema:
            default: 1
            type: number
        - name: limit
          required: false
          in: query
          description: Number of items per page
          schema:
            default: 10
            type: number
        - name: category
          required: false
          in: query
          description: Category of the file
          schema:
            type: string
        - name: tags
          required: false
          in: query
          description: Tags for the file
          schema:
            type: array
            items:
              type: string
        - name: uploadedBy
          required: false
          in: query
          description: User ID who uploaded the file
          schema:
            type: string
        - name: sortBy
          required: false
          in: query
          description: Field to sort by
          schema:
            default: createdAt
            type: string
        - name: sortOrder
          required: false
          in: query
          description: Sort order
          schema:
            default: desc
            type: string
            enum:
              - asc
              - desc
      responses:
        '200':
          description: ''
      summary: Get files with pagination and filtering
      tags: *ref_9
  /files/{fileId}:
    get:
      operationId: FilesController_getFileById
      parameters:
        - name: fileId
          required: true
          in: path
          description: File ID
          schema:
            type: string
      responses:
        '200':
          description: ''
      summary: Get file metadata by ID
      tags: *ref_9
    put:
      operationId: FilesController_updateFile
      parameters:
        - name: fileId
          required: true
          in: path
          description: File ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FileUpdateDto'
      responses:
        '200':
          description: ''
      summary: Update file metadata
      tags: *ref_9
    delete:
      operationId: FilesController_deleteFile
      parameters:
        - name: fileId
          required: true
          in: path
          description: File ID
          schema:
            type: string
      responses:
        '200':
          description: ''
      summary: Delete file
      tags: *ref_9
  /files/{fileId}/download:
    get:
      operationId: FilesController_downloadFile
      parameters:
        - name: fileId
          required: true
          in: path
          description: File ID
          schema:
            type: string
      responses:
        '200':
          description: ''
      summary: Download file by ID
      tags: *ref_9
  /files/render/{id}:
    get:
      operationId: FilesController_renderFile
      parameters:
        - name: id
          required: true
          in: path
          description: File ID
          schema:
            type: string
      responses:
        '200':
          description: File rendered successfully.
        '400':
          description: Failed to render file.
        '404':
          description: File not found.
      summary: Render file by ID in browser
      tags: *ref_9
  /files/images/{filename}:
    get:
      operationId: ImagesController_getImage
      parameters:
        - name: filename
          required: true
          in: path
          description: Image filename
          schema:
            type: string
      responses:
        '200':
          description: ''
      summary: Get an image file by filename
      tags:
        - images
  /question-pool/metrics:
    get:
      operationId: QuestionPoolMetricsController_getMetrics
      parameters: []
      responses:
        '200':
          description: Prometheus metrics in text format
          content:
            text/plain:
              schema:
                type: string
                example: >-
                  # HELP question_pool_query_duration_seconds Duration of
                  question pool queries in seconds

                  # TYPE question_pool_query_duration_seconds histogram

                  question_pool_query_duration_seconds_bucket{le="0.001",method="getRandomQuestions",cache_status="miss"}
                  0

                  ...
      summary: Get Prometheus metrics for question pool operations
      tags: &ref_10
        - Question Pool Metrics
  /question-pool/metrics/summary:
    get:
      operationId: QuestionPoolMetricsController_getPerformanceSummary
      parameters: []
      responses:
        '200':
          description: Performance summary with key metrics
          content:
            application/json:
              schema:
                type: object
                properties:
                  totalQueries:
                    type: number
                    description: Total number of queries executed
                  cacheHitRate:
                    type: number
                    description: Cache hit rate (0-1)
                  averageQueryTime:
                    type: number
                    description: Average query time in seconds
                  errorRate:
                    type: number
                    description: Error rate (0-1)
      summary: Get performance summary for question pool operations
      tags: *ref_10
  /question-pool/metrics/cache-stats:
    get:
      operationId: QuestionPoolMetricsController_getCacheStats
      parameters: []
      responses:
        '200':
          description: Cache statistics
          content:
            application/json:
              schema:
                type: object
                properties:
                  hitRate:
                    type: number
                    description: Cache hit rate (0-1)
                  totalHits:
                    type: number
                    description: Total cache hits
                  totalMisses:
                    type: number
                    description: Total cache misses
      summary: Get cache statistics for question pool
      tags: *ref_10
  /worksheets:
    post:
      operationId: WorksheetController_create
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateWorksheetDto'
      responses:
        '201':
          description: Worksheet created successfully
        '400':
          description: Bad request
      security: &ref_11
        - bearer: []
      summary: Create a new worksheet
      tags: &ref_12
        - Worksheet
    get:
      description: >-
        Retrieves worksheets with pagination support. Use page and pageSize
        parameters to control pagination.
      operationId: WorksheetController_findAll
      parameters:
        - name: page
          required: false
          in: query
          description: Page number for pagination (starts at 1)
          schema:
            default: 1
            example: 1
            type: number
        - name: pageSize
          required: false
          in: query
          description: Number of items per page
          schema:
            default: 10
            example: 10
            type: number
      responses:
        '200':
          description: Worksheets retrieved successfully
          content:
            application/json:
              schema:
                properties:
                  items:
                    type: array
                    description: Array of worksheet items for the current page
                    items:
                      type: object
                  meta:
                    type: object
                    description: Pagination metadata
                    properties:
                      page:
                        type: number
                        description: Current page number
                        example: 1
                      pageSize:
                        type: number
                        description: Number of items per page
                        example: 10
                      total:
                        type: number
                        description: Total number of items across all pages
                        example: 100
                      totalPages:
                        type: number
                        description: Total number of pages
                        example: 10
      security: *ref_11
      summary: Get all worksheets with pagination
      tags: *ref_12
  /worksheets/cache/metrics:
    get:
      operationId: WorksheetController_getCacheMetrics
      parameters: []
      responses:
        '200':
          description: Cache metrics retrieved successfully
      security: *ref_11
      summary: Get document cache metrics
      tags: *ref_12
  /worksheets/cache/warm:
    get:
      operationId: WorksheetController_triggerCacheWarming
      parameters: []
      responses:
        '200':
          description: Cache warming triggered successfully
      security: *ref_11
      summary: Manually trigger cache warming
      tags: *ref_12
  /worksheets/{id}:
    get:
      operationId: WorksheetController_findOne
      parameters:
        - name: id
          required: true
          in: path
          description: Worksheet ID
          schema:
            type: string
      responses:
        '200':
          description: Worksheet retrieved successfully
        '404':
          description: Worksheet not found
      security: *ref_11
      summary: Get worksheet by ID
      tags: *ref_12
    delete:
      operationId: WorksheetController_remove
      parameters:
        - name: id
          required: true
          in: path
          description: Worksheet ID
          schema:
            type: string
      responses:
        '204':
          description: Worksheet deleted successfully
        '403':
          description: Forbidden. User does not have permission to delete this worksheet.
        '404':
          description: Worksheet not found.
      security: *ref_11
      summary: Delete a worksheet
      tags: *ref_12
  /admin/monitoring/dashboard:
    get:
      description: >-
        Returns aggregated metrics including pool utilization, question reuse,
        generation times, validation rates, and cache performance. Requires
        admin role.
      operationId: MonitoringController_getDashboardMetrics
      parameters:
        - name: startDate
          required: false
          in: query
          description: Start date for the metrics range (ISO string)
          schema:
            example: '2024-01-01T00:00:00.000Z'
            type: string
        - name: endDate
          required: false
          in: query
          description: End date for the metrics range (ISO string)
          schema:
            example: '2024-01-31T23:59:59.999Z'
            type: string
        - name: timeframe
          required: false
          in: query
          description: Timeframe for aggregated metrics
          schema:
            default: daily
            example: daily
            type: string
            enum:
              - hourly
              - daily
              - weekly
              - monthly
      responses:
        '200':
          description: Dashboard metrics successfully retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DashboardMetricsResponseDto'
        '400':
          description: Bad Request - Invalid query parameters
        '401':
          description: Unauthorized - Invalid or missing token
        '403':
          description: Forbidden - Admin role required
      security: &ref_13
        - bearer: []
      summary: Get comprehensive dashboard metrics for question pool monitoring
      tags: &ref_14
        - Monitoring & Analytics
  /admin/monitoring/pool-utilization:
    get:
      description: >-
        Returns metrics about how effectively the question pool is being
        utilized
      operationId: MonitoringController_getPoolUtilization
      parameters:
        - name: startDate
          required: false
          in: query
          description: Start date for the metrics range (ISO string)
          schema:
            example: '2024-01-01T00:00:00.000Z'
            type: string
        - name: endDate
          required: false
          in: query
          description: End date for the metrics range (ISO string)
          schema:
            example: '2024-01-31T23:59:59.999Z'
            type: string
        - name: timeframe
          required: false
          in: query
          description: Timeframe for aggregated metrics
          schema:
            default: daily
            example: daily
            type: string
            enum:
              - hourly
              - daily
              - weekly
              - monthly
      responses:
        '200':
          description: Pool utilization metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PoolUtilizationResponseDto'
      security: *ref_13
      summary: Get pool utilization metrics
      tags: *ref_14
  /admin/monitoring/question-reuse:
    get:
      description: Returns metrics about how frequently questions are being reused
      operationId: MonitoringController_getQuestionReuse
      parameters:
        - name: startDate
          required: false
          in: query
          description: Start date for the metrics range (ISO string)
          schema:
            example: '2024-01-01T00:00:00.000Z'
            type: string
        - name: endDate
          required: false
          in: query
          description: End date for the metrics range (ISO string)
          schema:
            example: '2024-01-31T23:59:59.999Z'
            type: string
        - name: timeframe
          required: false
          in: query
          description: Timeframe for aggregated metrics
          schema:
            default: daily
            example: daily
            type: string
            enum:
              - hourly
              - daily
              - weekly
              - monthly
      responses:
        '200':
          description: Question reuse metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuestionReuseResponseDto'
      security: *ref_13
      summary: Get question reuse frequency metrics
      tags: *ref_14
  /admin/monitoring/generation-times:
    get:
      description: >-
        Returns comparison of generation times between pool selection and AI
        generation
      operationId: MonitoringController_getGenerationTimes
      parameters:
        - name: startDate
          required: false
          in: query
          description: Start date for the metrics range (ISO string)
          schema:
            example: '2024-01-01T00:00:00.000Z'
            type: string
        - name: endDate
          required: false
          in: query
          description: End date for the metrics range (ISO string)
          schema:
            example: '2024-01-31T23:59:59.999Z'
            type: string
        - name: timeframe
          required: false
          in: query
          description: Timeframe for aggregated metrics
          schema:
            default: daily
            example: daily
            type: string
            enum:
              - hourly
              - daily
              - weekly
              - monthly
      responses:
        '200':
          description: Generation time comparison metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerationTimeResponseDto'
      security: *ref_13
      summary: Get generation time comparison metrics
      tags: *ref_14
  /admin/monitoring/validation-metrics:
    get:
      description: >-
        Returns metrics about content validation success rates and issue
        breakdown
      operationId: MonitoringController_getValidationMetrics
      parameters:
        - name: startDate
          required: false
          in: query
          description: Start date for the metrics range (ISO string)
          schema:
            example: '2024-01-01T00:00:00.000Z'
            type: string
        - name: endDate
          required: false
          in: query
          description: End date for the metrics range (ISO string)
          schema:
            example: '2024-01-31T23:59:59.999Z'
            type: string
        - name: timeframe
          required: false
          in: query
          description: Timeframe for aggregated metrics
          schema:
            default: daily
            example: daily
            type: string
            enum:
              - hourly
              - daily
              - weekly
              - monthly
      responses:
        '200':
          description: Validation metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationResponseDto'
      security: *ref_13
      summary: Get validation success rate metrics
      tags: *ref_14
  /admin/monitoring/cache-metrics:
    get:
      description: >-
        Returns cache hit/miss ratios and performance metrics for specified
        cache type
      operationId: MonitoringController_getCacheMetrics
      parameters:
        - name: startDate
          required: false
          in: query
          description: Start date for the metrics range (ISO string)
          schema:
            example: '2024-01-01T00:00:00.000Z'
            type: string
        - name: endDate
          required: false
          in: query
          description: End date for the metrics range (ISO string)
          schema:
            example: '2024-01-31T23:59:59.999Z'
            type: string
        - name: timeframe
          required: false
          in: query
          description: Timeframe for aggregated metrics
          schema:
            default: daily
            example: daily
            type: string
            enum:
              - hourly
              - daily
              - weekly
              - monthly
        - name: cacheType
          required: false
          in: query
          description: Cache type to filter by
          schema:
            enum:
              - question_pool
              - worksheet_document
              - query_cache
            type: string
      responses:
        '200':
          description: Cache performance metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CacheResponseDto'
      security: *ref_13
      summary: Get cache performance metrics
      tags: *ref_14
  /admin/monitoring/events:
    get:
      description: >-
        Returns raw monitoring events for detailed analysis. Use with caution as
        this can return large datasets.
      operationId: MonitoringController_getEvents
      parameters:
        - name: startDate
          required: false
          in: query
          description: Start date for the query range (ISO string)
          schema:
            example: '2024-01-01T00:00:00.000Z'
            type: string
        - name: endDate
          required: false
          in: query
          description: End date for the query range (ISO string)
          schema:
            example: '2024-01-31T23:59:59.999Z'
            type: string
        - name: timeframe
          required: false
          in: query
          description: Timeframe for aggregated metrics
          schema:
            example: daily
            type: string
            enum:
              - hourly
              - daily
              - weekly
              - monthly
        - name: eventType
          required: false
          in: query
          description: Filter by specific event type
          schema:
            example: question_selection
            type: string
            enum:
              - question_selection
              - question_generation
              - validation_attempt
              - cache_interaction
              - worksheet_generation
              - distribution_analysis
        - name: userId
          required: false
          in: query
          description: Filter by user ID
          schema:
            example: user-123
            type: string
        - name: worksheetId
          required: false
          in: query
          description: Filter by worksheet ID
          schema:
            example: worksheet-456
            type: string
        - name: limit
          required: false
          in: query
          description: Maximum number of events to return
          schema:
            minimum: 1
            maximum: 10000
            example: 1000
            type: number
      responses:
        '200':
          description: Raw monitoring events
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    eventId:
                      type: string
                    type:
                      type: string
                    timestamp:
                      type: string
                      format: date-time
                    userId:
                      type: string
                    worksheetId:
                      type: string
                    eventData:
                      type: object
      security: *ref_13
      summary: Get raw monitoring events
      tags: *ref_14
  /options/types:
    post:
      operationId: OptionsController_createOptionType
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOptionTypeDto'
      responses:
        '201':
          description: Option type created successfully
        '400':
          description: Bad request
      summary: Create a new option type
      tags: &ref_15
        - Options
    get:
      operationId: OptionsController_findAllOptionTypes
      parameters: []
      responses:
        '200':
          description: Option types retrieved successfully
      summary: Get all option types
      tags: *ref_15
  /options/values:
    post:
      operationId: OptionsController_createOptionValue
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOptionValueDto'
      responses:
        '201':
          description: Option value created successfully
        '400':
          description: Bad request
      summary: Create a new option value
      tags: *ref_15
  /options/types/by-key/{key}:
    get:
      operationId: OptionsController_findOptionTypeByKey
      parameters:
        - name: key
          required: true
          in: path
          description: Option type key
          schema:
            type: string
      responses:
        '200':
          description: Option type retrieved successfully
        '404':
          description: Option type not found
      summary: Get option type by key
      tags: *ref_15
  /options/types/{id}:
    get:
      operationId: OptionsController_findOptionTypeById
      parameters:
        - name: id
          required: true
          in: path
          description: Option type ID
          schema:
            type: string
      responses:
        '200':
          description: Option type retrieved successfully
        '404':
          description: Option type not found
      summary: Get option type by ID
      tags: *ref_15
  /options/values/by-type/{typeId}:
    get:
      operationId: OptionsController_findOptionValuesByTypeId
      parameters:
        - name: typeId
          required: true
          in: path
          description: Option type ID
          schema:
            type: string
      responses:
        '200':
          description: Option values retrieved successfully
        '404':
          description: Option type not found
      summary: Get option values by type ID
      tags: *ref_15
  /options/subjects:
    post:
      operationId: OptionsController_createSubject
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSubjectDto'
      responses:
        '201':
          description: Subject created successfully
        '400':
          description: Bad request
      summary: Create a new subject
      tags: *ref_15
    get:
      operationId: OptionsController_findAllSubjects
      parameters: []
      responses:
        '200':
          description: Subjects retrieved successfully
      summary: Get all subjects
      tags: *ref_15
  /options/subjects/hierarchy:
    get:
      operationId: OptionsController_getSubjectHierarchy
      parameters:
        - name: topicId
          required: false
          in: query
          description: Filter by topic ID
          schema:
            example: 123e4567-e89b-12d3-a456-************
            type: string
        - name: gradeId
          required: false
          in: query
          description: Filter by grade ID
          schema:
            example: 123e4567-e89b-12d3-a456-************
            type: string
      responses:
        '200':
          description: Subject hierarchy retrieved successfully
        '404':
          description: Topic not found
      summary: Get complete subject hierarchy
      tags: *ref_15
  /options/subjects/by-parent/{parentId}:
    get:
      operationId: OptionsController_findSubjectsByParentId
      parameters:
        - name: parentId
          required: true
          in: path
          description: Parent subject ID
          schema:
            type: string
      responses:
        '200':
          description: Subjects retrieved successfully
      summary: Get subjects by parent ID
      tags: *ref_15
  /options/subjects/root:
    get:
      operationId: OptionsController_findRootSubjects
      parameters: []
      responses:
        '200':
          description: Root subjects retrieved successfully
      summary: Get root subjects (without parent)
      tags: *ref_15
  /options/subjects/by-topic/{topicId}:
    get:
      operationId: OptionsController_findSubjectsByTopicId
      parameters:
        - name: topicId
          required: true
          in: path
          description: Topic ID
          schema:
            type: string
        - name: type
          required: false
          in: query
          description: Filter by subject type
          schema:
            enum:
              - parent
              - child
            type: string
      responses:
        '200':
          description: Subjects retrieved successfully
        '404':
          description: Topic not found
      summary: Get subjects by topic ID
      tags: *ref_15
  /options/subjects/{id}:
    get:
      operationId: OptionsController_findSubjectById
      parameters:
        - name: id
          required: true
          in: path
          description: Subject ID
          schema:
            type: string
      responses:
        '200':
          description: Subject retrieved successfully
        '404':
          description: Subject not found
      summary: Get subject by ID
      tags: *ref_15
    put:
      operationId: OptionsController_updateSubject
      parameters:
        - name: id
          required: true
          in: path
          description: Subject ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSubjectDto'
      responses:
        '200':
          description: Subject updated successfully
        '404':
          description: Subject not found
      summary: Update a subject
      tags: *ref_15
    delete:
      operationId: OptionsController_deleteSubject
      parameters:
        - name: id
          required: true
          in: path
          description: Subject ID
          schema:
            type: string
      responses:
        '200':
          description: Subject deleted successfully
        '404':
          description: Subject not found
      summary: Delete a subject
      tags: *ref_15
  /options/values/{id}:
    delete:
      operationId: OptionsController_deleteOptionValue
      parameters:
        - name: id
          required: true
          in: path
          description: Option value ID
          schema:
            type: string
      responses:
        '204':
          description: Option value deleted successfully
        '400':
          description: Bad request
        '404':
          description: Option value not found
      summary: Delete an option value
      tags: *ref_15
  /options/seed-initial-data:
    post:
      operationId: OptionsController_seedInitialData
      parameters: []
      responses:
        '201':
          description: Initial data seeded successfully
        '400':
          description: Bad request
      summary: Seed initial option data
      tags: *ref_15
  /options/seed-math-subjects:
    post:
      operationId: OptionsController_seedMathSubjects
      parameters: []
      responses:
        '201':
          description: Math subjects seeded successfully
        '400':
          description: Bad request
      summary: Seed math subjects data for P5 and P6
      tags: *ref_15
  /options/seed-koobits-p5-math:
    post:
      operationId: OptionsController_seedKooBitsP5Math
      parameters: []
      responses:
        '201':
          description: KooBits P5 math subjects seeded successfully
        '400':
          description: Bad request
      summary: Seed P5 math subjects from KooBits curriculum
      tags: *ref_15
  /options/seed-p5-math-subjects-detailed:
    post:
      operationId: OptionsController_seedP5MathSubjectsDetailed
      parameters: []
      responses:
        '201':
          description: Detailed P5 math subjects seeded successfully
        '400':
          description: Bad request
      summary: Seed detailed P5 math subjects with all sub-topics
      tags: *ref_15
  /exams:
    post:
      operationId: ExamController_createExam
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateExamDto'
      responses:
        '201':
          description: ''
      tags: &ref_16
        - Exam
  /exams/{id}:
    get:
      operationId: ExamController_getExam
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
      tags: *ref_16
  /exams/{id}/submit:
    post:
      operationId: ExamController_submitExam
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitExamDto'
      responses:
        '201':
          description: ''
      tags: *ref_16
  /exams/by-worksheet/{worksheetId}:
    get:
      operationId: ExamController_getExamsByWorksheet
      parameters:
        - name: worksheetId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
      tags: *ref_16
info:
  title: EduSG API
  description: The EduSG API documentation
  version: '1.0'
  contact: {}
tags: []
servers: []
components:
  securitySchemes:
    bearer:
      scheme: bearer
      bearerFormat: JWT
      type: http
  schemas:
    EUserRole:
      type: string
      enum:
        - teacher
        - admin
        - student
        - school_manager
      description: User role
    CreateUserDto:
      type: object
      properties:
        name:
          type: string
          description: User full name
          example: John Doe
        email:
          type: string
          description: User email address
          example: <EMAIL>
        password:
          type: string
          description: User password (minimum 6 characters)
          example: password123
          minLength: 6
        role:
          description: User role
          example: teacher
          allOf:
            - $ref: '#/components/schemas/EUserRole'
        schoolId:
          type: string
          description: School ID (required for TEACHER, STUDENT, and SCHOOL_MANAGER roles)
          example: 123e4567-e89b-12d3-a456-************
      required:
        - name
        - email
        - password
        - role
    UpdateUserDto:
      type: object
      properties:
        name:
          type: string
          description: User full name
          example: John Doe
        email:
          type: string
          description: User email address
          example: <EMAIL>
        password:
          type: string
          description: User password (minimum 6 characters)
          example: password123
          minLength: 6
        role:
          description: User role
          example: teacher
          allOf:
            - $ref: '#/components/schemas/EUserRole'
        schoolId:
          type: string
          description: School ID (required for TEACHER, STUDENT, and SCHOOL_MANAGER roles)
          example: 123e4567-e89b-12d3-a456-************
    SignInDto:
      type: object
      properties:
        email:
          type: string
          description: User email address
          example: <EMAIL>
        password:
          type: string
          description: User password
          example: password123
      required:
        - email
        - password
    SignUpDto:
      type: object
      properties:
        role:
          type: string
          description: User role
          enum:
            - teacher
            - admin
            - student
            - school_manager
          default: teacher
        schoolId:
          type: string
          description: School ID (required for TEACHER, STUDENT, and SCHOOL_MANAGER roles)
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          description: User full name
          example: John Doe
        email:
          type: string
          description: User email address
          example: <EMAIL>
        password:
          type: string
          description: User password (minimum 6 characters)
          example: password123
          minLength: 6
      required:
        - role
        - name
        - email
        - password
    CreateSchoolDto:
      type: object
      properties:
        name:
          type: string
          description: The official name of the school
          example: Springfield Elementary School
        address:
          type: string
          description: The physical location of the school
          example: 123 Education St, Springfield
        phoneNumber:
          type: string
          description: Contact number for the school
          example: ******-123-4567
        registeredNumber:
          type: string
          description: Official registration or license number
          example: REG12345678
        email:
          type: string
          description: Official email address for communications
          example: <EMAIL>
        adminId:
          type: string
          description: ID of the school administrator (School Manager user)
          example: 123e4567-e89b-12d3-a456-************
        brandId:
          type: string
          description: ID of the associated brand
          example: 123e4567-e89b-12d3-a456-************
      required:
        - name
        - address
        - phoneNumber
        - registeredNumber
        - email
    User:
      type: object
      properties: {}
    Brand:
      type: object
      properties:
        logo:
          type: string
          description: The official logo or symbol representing the school
          example: https://example.com/logo.png
        color:
          type: string
          description: Brand color scheme or primary colors
          example: '#FF5733'
        image:
          type: string
          description: Additional brand imagery or visual assets
          example: https://example.com/image.png
      required:
        - logo
        - color
        - image
    School:
      type: object
      properties:
        name:
          type: string
          description: The official name of the school
          example: Springfield Elementary School
        address:
          type: string
          description: The physical location of the school
          example: 123 Education St, Springfield
        phoneNumber:
          type: string
          description: Contact number for the school
          example: ******-123-4567
        registeredNumber:
          type: string
          description: Official registration or license number
          example: REG12345678
        email:
          type: string
          description: Official email address for communications
          example: <EMAIL>
        admin:
          description: Reference to the school administrator (School Manager user)
          allOf:
            - $ref: '#/components/schemas/User'
        brand:
          description: Reference to the associated brand information
          allOf:
            - $ref: '#/components/schemas/Brand'
      required:
        - name
        - address
        - phoneNumber
        - registeredNumber
        - email
        - admin
        - brand
    UpdateSchoolDto:
      type: object
      properties:
        name:
          type: string
          description: The official name of the school
          example: Springfield Elementary School
        address:
          type: string
          description: The physical location of the school
          example: 123 Education St, Springfield
        phoneNumber:
          type: string
          description: Contact number for the school
          example: ******-123-4567
        registeredNumber:
          type: string
          description: Official registration or license number
          example: REG12345678
        email:
          type: string
          description: Official email address for communications
          example: <EMAIL>
        adminId:
          type: string
          description: ID of the school administrator (School Manager user)
          example: 123e4567-e89b-12d3-a456-************
        brandId:
          type: string
          description: ID of the associated brand
          example: 123e4567-e89b-12d3-a456-************
    NarrativeStructureResponseDto:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the narrative structure
          example: 123e4567-e89b-12d3-a456-************
        schoolId:
          type: string
          description: The school ID this narrative structure belongs to
          example: 123e4567-e89b-12d3-a456-************
        content:
          type: string
          description: The extracted narrative structure content
          example: >-
            This school follows a structured examination format with emphasis
            on...
        sourceFormatsCount:
          type: number
          description: Number of examination formats analyzed
          example: 3
        extractedAt:
          format: date-time
          type: string
          description: Timestamp when the narrative structure was extracted
        version:
          type: number
          description: Version number of the narrative structure
          example: 1
        extractionMetadata:
          type: object
          description: Metadata about the extraction process
          example:
            aiModel: gpt-4
            processingTime: 1500
            confidence: 0.95
        createdAt:
          format: date-time
          type: string
          description: Creation timestamp
        updatedAt:
          format: date-time
          type: string
          description: Last update timestamp
      required:
        - id
        - schoolId
        - content
        - sourceFormatsCount
        - extractedAt
        - version
        - createdAt
        - updatedAt
    ExtractNarrativeStructureResponseDto:
      type: object
      properties:
        success:
          type: boolean
          description: Success status of the extraction
          example: true
        message:
          type: string
          description: Status message
          example: Narrative structure extracted successfully
        narrativeStructure:
          description: The extracted narrative structure
          allOf:
            - $ref: '#/components/schemas/NarrativeStructureResponseDto'
        processedFormatsCount:
          type: number
          description: Number of examination formats processed
          example: 3
      required:
        - success
        - message
    BulkExtractNarrativeStructureResponseDto:
      type: object
      properties:
        success:
          type: boolean
          description: Overall success status
          example: true
        message:
          type: string
          description: Overall status message
          example: Bulk narrative structure extraction completed
        totalSchools:
          type: number
          description: Total number of schools processed
          example: 10
        successfulExtractions:
          type: number
          description: Number of schools successfully processed
          example: 8
        failedExtractions:
          type: number
          description: Number of schools that failed processing
          example: 2
        results:
          description: Details of the extraction results per school
          example:
            - schoolId: '123'
              success: true
              message: Success
            - schoolId: '456'
              success: false
              message: No examination formats found
          type: array
          items:
            type: object
      required:
        - success
        - message
        - totalSchools
        - successfulExtractions
        - failedExtractions
        - results
    CreateBrandDto:
      type: object
      properties:
        logo:
          type: string
          description: The official logo or symbol representing the school
          example: https://example.com/logo.png
        color:
          type: string
          description: Brand color scheme or primary colors
          example: '#FF5733'
        image:
          type: string
          description: Additional brand imagery or visual assets
          example: https://example.com/image.png
    UpdateBrandDto:
      type: object
      properties:
        logo:
          type: string
          description: The official logo or symbol representing the school
          example: https://example.com/logo.png
        color:
          type: string
          description: Brand color scheme or primary colors
          example: '#FF5733'
        image:
          type: string
          description: Additional brand imagery or visual assets
          example: https://example.com/image.png
    SubjectDataItem:
      type: object
      properties:
        label:
          type: string
          description: Label of the subject
          example: Speed
        items:
          description: Items within the subject
          example:
            - Speed Conversion
            - Time, Distance and Speed Relationship
          type: array
          items:
            type: string
      required:
        - label
        - items
    UserRequest:
      type: object
      properties:
        grade:
          type: string
          description: Grade level of the student
          example: Primary 3
        schoolId:
          type: string
          description: School ID for school-specific examination formats
          example: school-123
        topic:
          type: string
          description: Main subject area (e.g., Mathematics, Science, English)
          example: Mathematics
        subject:
          type: string
          description: >-
            Specific lesson content or sub-topic (e.g., "Dividing a whole number
            by a proper fraction")
          example: Dividing a whole number by a proper fraction
        parentSubject:
          type: string
          description: Chapter or unit (e.g., "Fractions", "Algebra")
          example: Fractions
        subjectData:
          description: Structured subject data with labels and items
          type: array
          items:
            $ref: '#/components/schemas/SubjectDataItem'
        difficulty:
          type: string
          description: Difficulty level of the exercise
          example: Medium
          enum:
            - Easy
            - Medium
            - Hard
        totalQuestions:
          type: number
          description: Total number of questions in the exercise
          example: 10
          minimum: 1
        isCustomQuestionCount:
          type: boolean
          description: Indicates if a custom question count was used
          example: true
        exerciseType:
          description: Types of exercises to generate
          example:
            - Multiple Choice
            - Open-ended
          type: array
          items:
            type: array
        exerciseTypeDistribution:
          type: object
          description: Distribution of questions by exercise type
          example:
            Multiple Choice: 5
            Open-ended: 3
        includeImages:
          type: boolean
          description: Whether to include images in the questions
          example: false
          default: false
        selectedOptions:
          type: object
          description: Selected options from the worksheet
        worksheetId:
          type: string
          description: ID of the worksheet, if applicable
          example: worksheet-xyz-123
      required:
        - grade
        - topic
        - difficulty
        - totalQuestions
        - exerciseType
    GenPromptDto:
      type: object
      properties:
        relevantContent:
          description: Relevant content to use for generating the exercise
          example:
            - Content about fractions
            - Content about decimals
          type: array
          items:
            type: array
        userRequest:
          description: User request details for the exercise
          allOf:
            - $ref: '#/components/schemas/UserRequest'
        worksheetId:
          type: string
          description: ID of the worksheet for tracking progress
      required:
        - relevantContent
        - userRequest
    AiServiceLog:
      type: object
      properties: {}
    FileUpdateDto:
      type: object
      properties:
        description:
          type: string
          description: Description of the file
        category:
          type: string
          description: Category of the file
        tags:
          description: Tags for the file
          type: array
          items:
            type: string
    SubjectItemDto:
      type: object
      properties:
        label:
          type: string
          description: Label of the subject
          example: Speed
        items:
          description: Items within the subject
          example:
            - Speed Conversion
            - Time, Distance and Speed Relationship
          type: array
          items:
            type: string
      required:
        - label
        - items
    QuestionTypeDto:
      type: object
      properties:
        label:
          type: string
          description: Label of the question type
          example: Fill Blank
        count:
          type: number
          description: Count of questions for this type
          example: 3
      required:
        - label
        - count
    WorksheetOptionDto:
      type: object
      properties:
        key:
          type: string
          description: Unique key for the worksheet option
          example: difficulty
        value:
          type: string
          description: Value of the worksheet option
          example: easy
        text:
          type: string
          description: Display text for the worksheet option (optional)
          example: Easy difficulty
        count:
          type: number
          description: Number of questions for this option (used with exerciseType)
          example: 5
      required:
        - key
        - value
    CreateWorksheetDto:
      type: object
      properties:
        title:
          type: string
          description: Title of the worksheet
          example: Math Worksheet - Fractions
        description:
          type: string
          description: Description of the worksheet
          example: A worksheet for practicing fractions
        grade:
          type: string
          description: Grade level
          example: P6
        topic:
          type: string
          description: Topic of the worksheet
          example: Mathematics
        subject:
          description: Subject items
          type: array
          items:
            $ref: '#/components/schemas/SubjectItemDto'
        level:
          type: string
          description: Difficulty level
          example: Medium
        language:
          type: string
          description: Language of the worksheet
          example: English
        question_count:
          type: string
          description: Number of questions
          example: '10'
        isCustomQuestionCount:
          type: boolean
          description: Flag indicating if question_count is a custom value
          example: true
        question_type:
          description: Question types
          type: array
          items:
            $ref: '#/components/schemas/QuestionTypeDto'
        options:
          description: Legacy options for the worksheet (for backward compatibility)
          type: array
          items:
            $ref: '#/components/schemas/WorksheetOptionDto'
        questionSourceStrategy:
          type: string
          description: Strategy for question sourcing
          enum:
            - pool-only
            - ai-only
            - hybrid
            - mixed
          example: hybrid
    PoolUtilizationResponseDto:
      type: object
      properties:
        timeframe:
          type: string
          description: Timeframe for the metrics
        timestamp:
          format: date-time
          type: string
          description: Timestamp when metrics were calculated
        totalUniqueQuestionsInPool:
          type: number
          description: Total unique questions in the pool
        uniqueQuestionsUsed:
          type: number
          description: Number of unique questions used
        utilizationRate:
          type: number
          description: Overall utilization rate (0-1)
        subjectBreakdown:
          type: object
          description: Breakdown by subject
          additionalProperties: true
      required:
        - timeframe
        - timestamp
        - totalUniqueQuestionsInPool
        - uniqueQuestionsUsed
        - utilizationRate
        - subjectBreakdown
    MostReusedQuestionDto:
      type: object
      properties:
        questionId:
          type: string
          description: Question ID
        usageCount:
          type: number
          description: Number of times this question was used
        questionType:
          type: string
          description: Type of the question
        subject:
          type: string
          description: Subject of the question
      required:
        - questionId
        - usageCount
        - questionType
        - subject
    QuestionReuseResponseDto:
      type: object
      properties:
        timeframe:
          type: string
          description: Timeframe for the metrics
        timestamp:
          format: date-time
          type: string
          description: Timestamp when metrics were calculated
        averageReuseFrequency:
          type: number
          description: Average reuse frequency across all questions
        mostReusedQuestions:
          description: Top 10 most reused questions
          type: array
          items:
            $ref: '#/components/schemas/MostReusedQuestionDto'
        reuseDistribution:
          type: object
          description: Distribution of reuse counts
          additionalProperties:
            type: number
      required:
        - timeframe
        - timestamp
        - averageReuseFrequency
        - mostReusedQuestions
        - reuseDistribution
    TimeStatsDto:
      type: object
      properties:
        average:
          type: number
          description: Average time in milliseconds
        p50:
          type: number
          description: 50th percentile time in milliseconds
        p95:
          type: number
          description: 95th percentile time in milliseconds
        p99:
          type: number
          description: 99th percentile time in milliseconds
      required:
        - average
        - p50
        - p95
        - p99
    GenerationTimeResponseDto:
      type: object
      properties:
        timeframe:
          type: string
          description: Timeframe for the metrics
        timestamp:
          format: date-time
          type: string
          description: Timestamp when metrics were calculated
        poolSelectionTime:
          description: Pool selection time statistics
          allOf:
            - $ref: '#/components/schemas/TimeStatsDto'
        aiGenerationTime:
          description: AI generation time statistics
          allOf:
            - $ref: '#/components/schemas/TimeStatsDto'
        comparisonRatio:
          type: number
          description: Ratio of pool time to AI time
      required:
        - timeframe
        - timestamp
        - poolSelectionTime
        - aiGenerationTime
        - comparisonRatio
    ValidationResponseDto:
      type: object
      properties:
        timeframe:
          type: string
          description: Timeframe for the metrics
        timestamp:
          format: date-time
          type: string
          description: Timestamp when metrics were calculated
        totalValidations:
          type: number
          description: Total number of validations performed
        successfulValidations:
          type: number
          description: Number of successful validations
        successRate:
          type: number
          description: Success rate (0-1)
        issueBreakdown:
          type: object
          description: Breakdown of issues by type
          additionalProperties: true
      required:
        - timeframe
        - timestamp
        - totalValidations
        - successfulValidations
        - successRate
        - issueBreakdown
    DashboardMetricsResponseDto:
      type: object
      properties:
        poolUtilization:
          description: Pool utilization metrics
          allOf:
            - $ref: '#/components/schemas/PoolUtilizationResponseDto'
        questionReuse:
          description: Question reuse metrics
          allOf:
            - $ref: '#/components/schemas/QuestionReuseResponseDto'
        generationTimeComparison:
          description: Generation time comparison metrics
          allOf:
            - $ref: '#/components/schemas/GenerationTimeResponseDto'
        validationMetrics:
          description: Validation success metrics
          allOf:
            - $ref: '#/components/schemas/ValidationResponseDto'
        cacheMetrics:
          type: object
          description: Cache performance metrics
          additionalProperties: true
        generatedAt:
          format: date-time
          type: string
          description: When the metrics were generated
        timeframe:
          type: string
          description: Timeframe used for aggregation
        dateRange:
          type: object
          description: Date range for the metrics
          properties:
            startDate:
              type: string
              format: date-time
            endDate:
              type: string
              format: date-time
      required:
        - poolUtilization
        - questionReuse
        - generationTimeComparison
        - validationMetrics
        - cacheMetrics
        - generatedAt
        - timeframe
        - dateRange
    CacheResponseDto:
      type: object
      properties:
        timeframe:
          type: string
          description: Timeframe for the metrics
        timestamp:
          format: date-time
          type: string
          description: Timestamp when metrics were calculated
        cacheType:
          type: string
          description: Cache type
        totalRequests:
          type: number
          description: Total number of cache requests
        hits:
          type: number
          description: Number of cache hits
        misses:
          type: number
          description: Number of cache misses
        hitRate:
          type: number
          description: Cache hit rate (0-1)
        averageResponseTime:
          type: number
          description: Average response time in milliseconds
      required:
        - timeframe
        - timestamp
        - cacheType
        - totalRequests
        - hits
        - misses
        - hitRate
        - averageResponseTime
    CreateOptionTypeDto:
      type: object
      properties:
        key:
          type: string
          description: Unique key for the option type
          example: difficulty_level
        label:
          type: string
          description: Display label for the option type
          example: Difficulty Level
        description:
          type: string
          description: Description of the option type
          example: Difficulty levels for exercises
        order:
          type: number
          description: Display order for the option type (optional)
          example: 1
      required:
        - key
        - label
        - description
    CreateOptionValueDto:
      type: object
      properties:
        name:
          type: string
          description: Name of the option value
          example: Easy
        optionTypeId:
          type: string
          description: UUID of the parent option type
          example: 123e4567-e89b-12d3-a456-************
        order:
          type: number
          description: Display order for the option value (optional)
          example: 1
        isActive:
          type: boolean
          description: Whether the option value is active (optional, defaults to true)
          example: true
          default: true
      required:
        - name
        - optionTypeId
    CreateSubjectDto:
      type: object
      properties:
        name:
          type: string
          description: Name of the subject
          example: Mathematics
        description:
          type: string
          description: Description of the subject
          example: Mathematics for primary school students
        type:
          type: string
          description: Type of subject (parent or child)
          enum:
            - parent
            - child
          default: child
        parentId:
          type: string
          description: ID of the parent subject
          example: 123e4567-e89b-12d3-a456-************
        topicIds:
          description: IDs of associated topics
          example:
            - 123e4567-e89b-12d3-a456-************
          type: array
          items:
            type: array
        gradeIds:
          description: IDs of associated grades
          example:
            - 123e4567-e89b-12d3-a456-************
          type: array
          items:
            type: array
      required:
        - name
    CreateExamDto:
      type: object
      properties: {}
    SubmitExamDto:
      type: object
      properties: {}
