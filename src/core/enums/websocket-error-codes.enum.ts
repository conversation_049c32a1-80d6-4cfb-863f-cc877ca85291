/**
 * WebSocket error codes for real-time error communication
 * Used in worksheet generation and question pool selection processes
 */
export enum WebSocketErrorCode {
  // Database related errors
  DATABASE_UNAVAILABLE = 'DATABASE_UNAVAILABLE',
  DATABASE_CONNECTION_FAILED = 'DATABASE_CONNECTION_FAILED',
  DATABASE_TIMEOUT = 'DATABASE_TIMEOUT',
  DATABASE_QUERY_FAILED = 'DATABASE_QUERY_FAILED',
  
  // Question pool related errors
  INSUFFICIENT_QUESTIONS = 'INSUFFICIENT_QUESTIONS',
  QUESTION_POOL_EMPTY = 'QUESTION_POOL_EMPTY',
  QUESTION_POOL_UNAVAILABLE = 'QUESTION_POOL_UNAVAILABLE',
  QUESTION_VALIDATION_FAILED = 'QUESTION_VALIDATION_FAILED',
  
  // AI service related errors
  AI_SERVICE_FAILED = 'AI_SERVICE_FAILED',
  OPENAI_SERVICE_FAILED = 'OPENAI_SERVICE_FAILED',
  GOOGLE_AI_SERVICE_FAILED = 'GOOGLE_AI_SERVICE_FAILED',
  AI_GENERATION_TIMEOUT = 'AI_GENERATION_TIMEOUT',
  AI_RESPONSE_INVALID = 'AI_RESPONSE_INVALID',
  ALL_AI_SERVICES_FAILED = 'ALL_AI_SERVICES_FAILED',
  
  // Fallback and recovery errors
  INSUFFICIENT_QUESTIONS_NO_FALLBACK = 'INSUFFICIENT_QUESTIONS_NO_FALLBACK',
  CACHED_CONTENT_UNAVAILABLE = 'CACHED_CONTENT_UNAVAILABLE',
  FALLBACK_CHAIN_EXHAUSTED = 'FALLBACK_CHAIN_EXHAUSTED',
  
  // Configuration and system errors
  INVALID_CONFIGURATION = 'INVALID_CONFIGURATION',
  FEATURE_DISABLED = 'FEATURE_DISABLED',
  MAX_RETRIES_REACHED = 'MAX_RETRIES_REACHED',
  SYSTEM_OVERLOAD = 'SYSTEM_OVERLOAD',
  
  // Worksheet generation specific errors
  WORKSHEET_GENERATION_FAILED = 'WORKSHEET_GENERATION_FAILED',
  DOCUMENT_PROCESSING_FAILED = 'DOCUMENT_PROCESSING_FAILED',
  CONTENT_VALIDATION_FAILED = 'CONTENT_VALIDATION_FAILED',
  
  // Generic errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
}

/**
 * WebSocket error event types
 */
export enum WebSocketErrorEvent {
  WORKSHEET_GENERATION_ERROR = 'worksheet:generation:error',
  DATABASE_ERROR = 'database:error',
  AI_SERVICE_ERROR = 'ai:service:error',
  QUESTION_POOL_ERROR = 'question:pool:error',
  SYSTEM_ERROR = 'system:error',
}

/**
 * Structured error payload for WebSocket communication
 */
export interface WebSocketErrorPayload {
  errorCode: WebSocketErrorCode;
  message: string;
  details?: {
    worksheetId?: string;
    userId?: string;
    timestamp: string;
    context?: Record<string, any>;
    retryable?: boolean;
    suggestedAction?: string;
  };
}

/**
 * Error severity levels for WebSocket errors
 */
export enum WebSocketErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Helper function to create structured WebSocket error payload
 */
export function createWebSocketErrorPayload(
  errorCode: WebSocketErrorCode,
  message: string,
  details?: Partial<WebSocketErrorPayload['details']>
): WebSocketErrorPayload {
  return {
    errorCode,
    message,
    details: {
      timestamp: new Date().toISOString(),
      retryable: false,
      ...details,
    },
  };
}

/**
 * Error code to severity mapping
 */
export const ERROR_SEVERITY_MAP: Record<WebSocketErrorCode, WebSocketErrorSeverity> = {
  [WebSocketErrorCode.DATABASE_UNAVAILABLE]: WebSocketErrorSeverity.CRITICAL,
  [WebSocketErrorCode.DATABASE_CONNECTION_FAILED]: WebSocketErrorSeverity.HIGH,
  [WebSocketErrorCode.DATABASE_TIMEOUT]: WebSocketErrorSeverity.MEDIUM,
  [WebSocketErrorCode.DATABASE_QUERY_FAILED]: WebSocketErrorSeverity.MEDIUM,
  
  [WebSocketErrorCode.INSUFFICIENT_QUESTIONS]: WebSocketErrorSeverity.MEDIUM,
  [WebSocketErrorCode.QUESTION_POOL_EMPTY]: WebSocketErrorSeverity.HIGH,
  [WebSocketErrorCode.QUESTION_POOL_UNAVAILABLE]: WebSocketErrorSeverity.HIGH,
  [WebSocketErrorCode.QUESTION_VALIDATION_FAILED]: WebSocketErrorSeverity.MEDIUM,
  
  [WebSocketErrorCode.AI_SERVICE_FAILED]: WebSocketErrorSeverity.MEDIUM,
  [WebSocketErrorCode.OPENAI_SERVICE_FAILED]: WebSocketErrorSeverity.MEDIUM,
  [WebSocketErrorCode.GOOGLE_AI_SERVICE_FAILED]: WebSocketErrorSeverity.MEDIUM,
  [WebSocketErrorCode.AI_GENERATION_TIMEOUT]: WebSocketErrorSeverity.MEDIUM,
  [WebSocketErrorCode.AI_RESPONSE_INVALID]: WebSocketErrorSeverity.MEDIUM,
  [WebSocketErrorCode.ALL_AI_SERVICES_FAILED]: WebSocketErrorSeverity.HIGH,
  
  [WebSocketErrorCode.INSUFFICIENT_QUESTIONS_NO_FALLBACK]: WebSocketErrorSeverity.HIGH,
  [WebSocketErrorCode.CACHED_CONTENT_UNAVAILABLE]: WebSocketErrorSeverity.MEDIUM,
  [WebSocketErrorCode.FALLBACK_CHAIN_EXHAUSTED]: WebSocketErrorSeverity.CRITICAL,
  
  [WebSocketErrorCode.INVALID_CONFIGURATION]: WebSocketErrorSeverity.HIGH,
  [WebSocketErrorCode.FEATURE_DISABLED]: WebSocketErrorSeverity.LOW,
  [WebSocketErrorCode.MAX_RETRIES_REACHED]: WebSocketErrorSeverity.HIGH,
  [WebSocketErrorCode.SYSTEM_OVERLOAD]: WebSocketErrorSeverity.CRITICAL,
  
  [WebSocketErrorCode.WORKSHEET_GENERATION_FAILED]: WebSocketErrorSeverity.HIGH,
  [WebSocketErrorCode.DOCUMENT_PROCESSING_FAILED]: WebSocketErrorSeverity.MEDIUM,
  [WebSocketErrorCode.CONTENT_VALIDATION_FAILED]: WebSocketErrorSeverity.MEDIUM,
  
  [WebSocketErrorCode.UNKNOWN_ERROR]: WebSocketErrorSeverity.MEDIUM,
  [WebSocketErrorCode.INTERNAL_SERVER_ERROR]: WebSocketErrorSeverity.CRITICAL,
};
