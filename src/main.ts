import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import * as fs from 'fs';
import * as yaml from 'js-yaml';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // Configure Swagger
  const config = new DocumentBuilder()
    .setTitle('EduSG API')
    .setDescription('The EduSG API documentation')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);

  // Export OpenAPI as YAML
  const yamlString = yaml.dump(document);
  fs.writeFileSync('./openapi-spec.yaml', yamlString);

  // Also serve Swagger UI
  SwaggerModule.setup('api', app, document);

  await app.listen(process.env.NESTJS_PORT ?? 3000);
  console.log(`Application is running on: ${await app.getUrl()}`);
  console.log('OpenAPI specification exported to openapi-spec.yaml');
}
bootstrap();
