import { <PERSON><PERSON><PERSON>, Column, OneToMany, ManyToOne, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { WorksheetOption } from './worksheet-option.entity';
import BaseEntity from '../../../core/entities/base-entity';
import { School } from '../../school/entities/school.entity';

export enum WorksheetGeneratingStatus {
  PENDING = 'Pending',
  GENERATED = 'Generated',
  ERROR = 'Error',
}

@Entity('worksheets')
export class Worksheet extends BaseEntity {
  @Column({ nullable: true })
  title: string;

  @Column({ nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: WorksheetGeneratingStatus,
    default: WorksheetGeneratingStatus.PENDING,
  })
  generatingStatus: WorksheetGeneratingStatus;

  @Column({ type: 'json', nullable: true })
  subjectData: any;

  @OneToMany(() => WorksheetOption, (wo) => wo.worksheet, { cascade: true })
  selectedOptions: WorksheetOption[];

  @ManyToOne(() => School, { nullable: true }) // Assuming a worksheet might not always be linked to a school
  @JoinColumn({ name: 'schoolId' })
  school: School;

  @Column({ nullable: true })
  schoolId: string;
}
