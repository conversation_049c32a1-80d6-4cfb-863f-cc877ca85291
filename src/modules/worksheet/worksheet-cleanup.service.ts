import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Worksheet } from './entities/worksheet.entity';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WorksheetDocument } from '../mongodb/schemas/worksheet-document.schema';
import { WorksheetPromptResult } from '../mongodb/schemas/worksheet-prompt-result.schema';
import { DocumentsService } from '../documents/documents.service';

@Injectable()
export class WorksheetCleanupService {
  private readonly logger = new Logger(WorksheetCleanupService.name);

  // Common topics and grades for cache warming
  private readonly popularTopics = [
    'Mathematics', 'Science', 'English', 'History',
    'Geography', 'Physics', 'Chemistry', 'Biology',
    'Algebra', 'Geometry', 'Calculus', 'Grammar',
    'Literature', 'Vocabulary', 'Fractions', 'Decimals'
  ];

  private readonly popularGrades = [
    'P1', 'P2', 'P3', 'P4', 'P5', 'P6',
    'S1', 'S2', 'S3', 'S4', 'S5',
    'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6',
    'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'
  ];

  constructor(
    @InjectRepository(Worksheet)
    private worksheetRepository: Repository<Worksheet>,
    @InjectModel(WorksheetDocument.name)
    private worksheetDocumentModel: Model<WorksheetDocument>,
    @InjectModel(WorksheetPromptResult.name)
    private worksheetPromptResultModel: Model<WorksheetPromptResult>,
    private documentsService: DocumentsService,
  ) {}

  @Cron(CronExpression.EVERY_HOUR)
  async handleCleanup() {
    this.logger.log('Start worksheet cleanup job...');
    const worksheets = await this.worksheetRepository.find();

    for (const worksheet of worksheets) {
      const [document, promptResult] = await Promise.all([
        this.worksheetDocumentModel.findOne({ worksheetId: worksheet.id }),
        this.worksheetPromptResultModel.findOne({ worksheetId: worksheet.id }),
      ]);

      if (!document || !promptResult) {
        this.logger.warn(
          `Deleting invalid worksheet: ${worksheet.id} (document: ${!!document}, promptResult: ${!!promptResult})`,
        );
        await this.worksheetRepository.delete(worksheet.id);
        if (document)
          await this.worksheetDocumentModel.deleteOne({
            worksheetId: worksheet.id,
          });
        if (promptResult)
          await this.worksheetPromptResultModel.deleteOne({
            worksheetId: worksheet.id,
          });
      }
    }
    this.logger.log('Worksheet cleanup job completed.');
  }

  /**
   * Refreshes expired document caches
   * Runs daily at midnight
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async refreshExpiredCaches() {
    this.logger.log('Starting cache refresh job...');

    try {
      // Find documents that will expire in the next 24 hours
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);

      const expiringDocuments = await this.worksheetDocumentModel.find({
        expiresAt: { $lt: tomorrow }
      }).sort({ hitCount: -1 }).limit(20); // Focus on most frequently used documents

      this.logger.log(`Found ${expiringDocuments.length} documents expiring soon`);

      // Refresh each document
      for (const doc of expiringDocuments) {
        try {
          // Only refresh documents that have been accessed multiple times
          if (doc.hitCount > 2) {
            this.logger.debug(`Refreshing document cache for topic: ${doc.topic}, grade: ${doc.grade}`);

            // Create the same query that was used originally
            const query = `Help me query subject teaching in ${doc.topic} SYLLABUS ${doc.grade}`;

            // Get fresh results from Pinecone
            const freshResults = await this.documentsService.queryDocuments(
              query,
              5,
              60000,
              doc.topic, // Use topic as category
              false // Use cache if available
            );

            // Update the document with fresh results and reset TTL
            const newExpiresAt = new Date();
            newExpiresAt.setDate(newExpiresAt.getDate() + 7);

            await this.worksheetDocumentModel.updateOne(
              { _id: doc._id },
              {
                documentResult: freshResults,
                expiresAt: newExpiresAt,
                updatedAt: new Date()
              }
            );

            this.logger.debug(`Successfully refreshed document cache for ${doc.topic}, grade ${doc.grade}`);
          }
        } catch (error) {
          this.logger.error(`Error refreshing document cache: ${error.message}`, error.stack);
        }
      }

      this.logger.log('Cache refresh job completed');
    } catch (error) {
      this.logger.error(`Error in cache refresh job: ${error.message}`, error.stack);
    }
  }

  /**
   * Warms the cache with popular topics and grades
   * Runs weekly on Sunday at 2 AM
   */
  @Cron(CronExpression.EVERY_WEEK)
  async warmCache() {
    this.logger.log('Starting cache warming job...');

    try {
      // Get the most frequently used topics and grades from existing worksheets
      const topTopics = await this.getTopUsedTopics(5);
      const topGrades = await this.getTopUsedGrades(5);

      // Combine with predefined popular topics and grades
      const topicsToWarm = [...new Set([...topTopics, ...this.popularTopics.slice(0, 5)])];
      const gradesToWarm = [...new Set([...topGrades, ...this.popularGrades.slice(0, 5)])];

      this.logger.log(`Warming cache for ${topicsToWarm.length} topics and ${gradesToWarm.length} grades`);

      // Process a limited number of combinations to avoid overloading
      const maxCombinations = 10;
      let processedCount = 0;

      for (const topic of topicsToWarm) {
        for (const grade of gradesToWarm) {
          if (processedCount >= maxCombinations) break;

          try {
            // Check if we already have a fresh cache for this combination
            const existingCache = await this.worksheetDocumentModel.findOne({
              topic,
              grade,
              expiresAt: { $gt: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000) } // More than 5 days left
            });

            if (!existingCache) {
              this.logger.debug(`Warming cache for topic: ${topic}, grade: ${grade}`);

              // Create query
              const query = `Help me query subject teaching in ${topic} SYLLABUS ${grade}`;

              // Get results from Pinecone
              const results = await this.documentsService.queryDocuments(
                query,
                5,
                60000,
                topic, // Use topic as category
                false // Use cache if available
              );

              // Save to cache
              const worksheetDocument = new this.worksheetDocumentModel({
                worksheetId: `cache-warming-${Date.now()}`, // Placeholder ID
                topic,
                grade,
                documentResult: results,
                fromCache: false,
                hitCount: 1
              });

              await worksheetDocument.save();
              this.logger.debug(`Successfully warmed cache for ${topic}, grade ${grade}`);
              processedCount++;
            } else {
              this.logger.debug(`Cache already fresh for ${topic}, grade ${grade}`);
            }
          } catch (error) {
            this.logger.error(`Error warming cache for ${topic}, grade ${grade}: ${error.message}`, error.stack);
          }
        }
      }

      this.logger.log(`Cache warming job completed. Processed ${processedCount} combinations.`);
    } catch (error) {
      this.logger.error(`Error in cache warming job: ${error.message}`, error.stack);
    }
  }

  /**
   * Gets the most frequently used topics from worksheets
   */
  private async getTopUsedTopics(limit: number = 5): Promise<string[]> {
    try {
      // Aggregate to find most common topics
      const topicCounts = await this.worksheetDocumentModel.aggregate([
        { $group: { _id: "$topic", count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: limit }
      ]);

      return topicCounts.map(item => item._id);
    } catch (error) {
      this.logger.error(`Error getting top topics: ${error.message}`);
      return [];
    }
  }

  /**
   * Gets the most frequently used grades from worksheets
   */
  private async getTopUsedGrades(limit: number = 5): Promise<string[]> {
    try {
      // Aggregate to find most common grades
      const gradeCounts = await this.worksheetDocumentModel.aggregate([
        { $group: { _id: "$grade", count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: limit }
      ]);

      return gradeCounts.map(item => item._id);
    } catch (error) {
      this.logger.error(`Error getting top grades: ${error.message}`);
      return [];
    }
  }
}
