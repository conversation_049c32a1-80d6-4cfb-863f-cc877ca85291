import { Injectable, Logger, forwardRef, Inject } from '@nestjs/common';
import { GenImageService } from './gen-image.service';
import { BuildPromptService } from '../build-prompt/build-prompt.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WorksheetPromptResult } from '../mongodb/schemas/worksheet-prompt-result.schema';
import { SocketGateway } from '../socket/socket.gateway';
import { ExerciseQuestionItem } from '../prompt/interfaces/exercise-result.interface';
import { JSDOM } from 'jsdom';

/**
 * Service for processing batches of images
 *
 * IMPORTANT: This service works with GenImageService to process images in batches.
 * The BATCH_SIZE constant in saveImageCallback must match MAX_BATCH_SIZE in gen-image.service.ts.
 * If MAX_BATCH_SIZE in gen-image.service.ts changes, update BATCH_SIZE here as well.
 */
@Injectable()
export class BatchImageService {
  private readonly logger = new Logger(BatchImageService.name);

  constructor(
    @Inject(forwardRef(() => GenImageService))
    private genImageService: GenImageService,
    @Inject(forwardRef(() => BuildPromptService))
    private buildPromptService: BuildPromptService,
    @InjectModel(WorksheetPromptResult.name)
    private worksheetPromptResultModel: Model<WorksheetPromptResult>,
    private socketGateway: SocketGateway
  ) {}

  /**
   * Processes a batch of image prompts for a worksheet
   * @param worksheetId The ID of the worksheet
   * @param topic Optional topic for specialized image generation
   * @returns True if successful, false otherwise
   */
  async processBatchImages(worksheetId: string, topic?: string): Promise<boolean> {
    try {
      this.logger.log(`Starting batch image processing for worksheet ${worksheetId}`);

      // Get the worksheet prompt result
      const promptResult = await this.worksheetPromptResultModel.findOne({ worksheetId });

      if (!promptResult || !promptResult.promptResult || !promptResult.promptResult.result) {
        this.logger.error(`No prompt result found for worksheet ${worksheetId}`);
        return false;
      }

      const questions = promptResult.promptResult.result as ExerciseQuestionItem[];

      if (!questions || questions.length === 0) {
        this.logger.error(`No questions found for worksheet ${worksheetId}`);
        return false;
      }

      this.logger.log(`Found ${questions.length} questions for worksheet ${worksheetId}`);

      // Collect all image prompts
      let imagePrompts: { prompt: string; index: number }[] = [];

      // Emit initial image generation progress
      this.socketGateway.emitWorksheetProgress(
        worksheetId,
        0,
        questions.length,
        {
          imageGenerationStarted: true,
          totalQuestions: questions.length,
          totalImagePrompts: 0, // Will be updated after collecting prompts
          stage: 'starting'
        }
      );

      // Using filter and map instead of for loop to collect image prompts
      imagePrompts = questions
        .map((question, i) => {
          // Skip questions that don't need images
          if (this.shouldSkipImageGeneration(question.imagePrompt, question.content)) {
            return null;
          }

          // Determine what content to use for image generation
          let imagePrompt = question.imagePrompt && question.imagePrompt.trim() !== '' ? question.imagePrompt.trim() : '';
          let questionContent = question.content ? question.content.trim() : '';
          let questionType = question.type || '';

          // Convert HTML and MathML to plain text
          if (imagePrompt.includes('<') && imagePrompt.includes('>')) {
            this.logger.log(`Converting HTML/MathML in image prompt to plain text`);
            imagePrompt = this.convertHtmlToPlainText(imagePrompt);
          }

          if (questionContent.includes('<') && questionContent.includes('>')) {
            this.logger.log(`Converting HTML/MathML in question content to plain text`);
            questionContent = this.convertHtmlToPlainText(questionContent);
          }

          // Check if this is a math question
          const isMathQuestion = this.isMathQuestion(questionContent, imagePrompt);

          // No longer checking for fill-in-the-blank questions

          let imageContent = '';

          if (isMathQuestion) {
            // For math questions, prefer to use the question content
            imageContent = questionContent;

            // If the question content is too short or empty, use the image prompt
            if (!imageContent || imageContent.length < 20) {
              imageContent = imagePrompt;
            }
          } else {
            // For non-math questions, use the detailed prompt logic
            const hasDetailedPrompt = imagePrompt.length > 20 &&
              (imagePrompt.includes('m') || imagePrompt.includes('cm') ||
               imagePrompt.includes('km') || imagePrompt.includes('mm') ||
               imagePrompt.includes('square') || imagePrompt.includes('angle') ||
               imagePrompt.includes('degree'));

            // Use the image prompt if it's detailed enough, otherwise use the question content
            imageContent = hasDetailedPrompt ? imagePrompt : questionContent;
          }

          if (!imageContent || imageContent.trim() === '') {
            return null;
          }

          // Clean up the image content
          let cleanedContent = imageContent
            .replace(/no overlapping/gi, '')
            .replace(/no overlap/gi, '')
            .replace(/labels should not overlap/gi, '')
            .replace(/ensure nothing is cut off/gi, '')
            .trim();

          // Remove phrases that indicate no image is needed for math problems
          cleanedContent = cleanedContent
            .replace(/no (specific )?(image|diagram|illustration) (is )?(required|needed) for this/gi, '')
            .replace(/no (image|diagram|illustration) (is )?(required|needed)/gi, '')
            .replace(/not (required|needed)/gi, '')
            .trim();

          // Create the user prompt - keep it simple as it will be part of a batch
          let userPrompt = cleanedContent;

          // For math questions, enhance the prompt to get better diagrams
          if (isMathQuestion) {
            userPrompt = `Create a clear, educational diagram for this math problem: ${cleanedContent}`;
          } else {
            userPrompt = cleanedContent;
          }

          // No longer adding fill-in-the-blank specific instructions

          // For questions involving vehicles, speed, time, or distance, add specific instructions
          const hasVehicleJourney =
            (cleanedContent.toLowerCase().includes('car') ||
             cleanedContent.toLowerCase().includes('train') ||
             cleanedContent.toLowerCase().includes('bus') ||
             cleanedContent.toLowerCase().includes('vehicle')) &&
            (cleanedContent.toLowerCase().includes('km/h') ||
             cleanedContent.toLowerCase().includes('speed') ||
             cleanedContent.toLowerCase().includes('hour') ||
             cleanedContent.toLowerCase().includes('distance'));

          if (hasVehicleJourney) {
            userPrompt += " This involves a vehicle journey. Include a clear visual of the vehicle, any speed indicators (like a speedometer showing the exact speed), time duration (like a clock showing the hours), and distance markers mentioned in the question.";
          }

          return {
            prompt: userPrompt,
            index: i,
            questionType: questionType // Include question type for later use
          };
        })
        .filter(item => item !== null);

      if (imagePrompts.length === 0) {
        this.logger.log(`No image prompts found for worksheet ${worksheetId}`);

        // Emit completion event
        this.socketGateway.emitWorksheetProgress(
          worksheetId,
          questions.length,
          questions.length,
          { imageGenerationComplete: true }
        );

        return true;
      }

      // Update progress with the actual number of image prompts
      this.socketGateway.emitWorksheetProgress(
        worksheetId,
        0,
        questions.length,
        {
          imageGenerationStarted: true,
          totalQuestions: questions.length,
          totalImagePrompts: imagePrompts.length,
          stage: 'processing'
        }
      );

      this.logger.log(`Collected ${imagePrompts.length} image prompts for worksheet ${worksheetId}`);

      // Create the system prompt for batch processing
      let baseSystemPrompt = this.buildPromptService.enhanceSvgPrompt('', topic);

      // Add batch processing instructions with special handling for different question types
      const systemPrompt = `${baseSystemPrompt}\n\nYou will receive an array of prompts for generating SVG images. For each prompt, create a clear, educational SVG diagram. Each SVG should be properly formatted with viewBox, width, height attributes. Ensure diagrams are clear, with no overlapping elements, and nothing cut off.\n\nFor ${topic || 'educational'} diagrams, use appropriate colors, labels, and visual elements to clearly illustrate each concept.

      # SPECIFIC QUESTION TYPE INSTRUCTIONS

      ## For Vehicle Journey Problems:
      - Include a clear visual of the vehicle mentioned (car, train, bus, etc.)
      - If speed is mentioned, include a speedometer/gauge showing the exact speed value
      - If time is mentioned, include a clock or timer showing the duration
      - If distance is mentioned, include a distance marker or route map with the exact distance
      - For problems involving speed, time, and distance, show all three elements clearly
      - Position these elements in a logical arrangement that helps understand their relationship

      ## For Math Problems:
      - Create clear, accurate diagrams that illustrate the mathematical concepts
      - For geometry problems, draw precise shapes with labeled dimensions
      - For coordinate geometry, include properly labeled axes
      - For speed/distance problems, use visual representations of the scenario
      - For financial problems, use appropriate charts or diagrams
      - For fraction problems, show visual representations of the fractions
      - For area and volume problems, clearly illustrate the shapes with dimensions
      - Always include relevant measurements, angles, or numerical values in the diagram
      - Use different colors to distinguish between different elements

      # GENERAL REQUIREMENTS FOR ALL DIAGRAMS
      - ALWAYS include visual representations of ALL key numerical values mentioned in the question
      - For questions with formulas, clearly show the formula with all variables labeled
      - For questions with units (km/h, meters, etc.), visually represent these units
      - Ensure all dimensions are proportionally accurate (e.g., 12m should be twice as long as 6m)
      - Position all labels with sufficient clearance from any line or object
      - Prevent ANY overlap between labels and other elements
      - For measurements, include units (e.g., "5 cm" not just "5")
      - For speeds, always include the unit (e.g., "240 km/h")
      - For time durations, clearly label with appropriate units (e.g., "3 hours")

      Note that some math problems don't require images, particularly those involving:
      - Simple arithmetic calculations
      - Whole number operations without visual elements
      - Place value questions
      - Ratio calculations without visual context
      - Order of operations
      - Mental math

      For these types of problems, our system will filter them out before sending them to you. If you receive a math problem, it has been determined to benefit from visualization.`;

      // Generate all images in batch, but with a callback to save each image as it's generated
      const promptsOnly = imagePrompts.map(item => item.prompt);

      // Create a callback function to save each image as it's generated
      const saveImageCallback = async (svgResult: string, batchIndex: number, imageIndex: number) => {
        try {
          // Get the batch size from the GenImageService (currently 5)
          const BATCH_SIZE = 5; // This should match MAX_BATCH_SIZE in gen-image.service.ts

          // Calculate the position in the imagePrompts array
          const promptsArrayIndex = batchIndex * BATCH_SIZE + imageIndex;

          // Get the original index in the questions array
          const originalIndex = imagePrompts[promptsArrayIndex]?.index;

          this.logger.log(`Image callback: batch=${batchIndex}, imageIndex=${imageIndex}, promptsArrayIndex=${promptsArrayIndex}, originalQuestionIndex=${originalIndex}`);

          if (originalIndex === undefined) {
            this.logger.error(`Invalid index for image at batch ${batchIndex}, index ${imageIndex}, calculated promptsArrayIndex=${promptsArrayIndex}`);
            return;
          }

          // Verify the original index is within the valid range for the questions array
          if (originalIndex < 0 || originalIndex >= questions.length) {
            this.logger.error(`Invalid question index: ${originalIndex} is outside the valid range (0-${questions.length - 1})`);
            return;
          }

          // Update the image in the questions array
          if (svgResult && svgResult.includes('<svg')) {
            questions[originalIndex].image = svgResult;
            this.logger.log(`Successfully saved SVG image for question ${originalIndex + 1}`);
          } else {
            questions[originalIndex].image = '';
            this.logger.log(`No valid SVG found for question ${originalIndex + 1}, setting empty image`);
          }

          // Update just this question in MongoDB
          this.worksheetPromptResultModel.findOneAndUpdate(
            { worksheetId },
            { $set: { [`promptResult.result.${originalIndex}.image`]: questions[originalIndex].image } }
          ).exec();

          // Emit a socket event with the updated question
          this.socketGateway.emitWorksheetProgress(
            worksheetId,
            originalIndex + 1, // Current progress (1-based for display)
            questions.length,
            {
              imageGenerationProgress: true,
              updatedQuestion: questions[originalIndex],
              questionIndex: originalIndex,
              stage: 'processing'
            }
          );

          this.logger.log(`Saved image for question ${originalIndex + 1}/${questions.length} in worksheet ${worksheetId}`);
        } catch (error) {
          this.logger.error(`Error saving individual image: ${error.message}`, error.stack);
        }
      };

      const svgResults = await this.genImageService.generateBatchImages(
        promptsOnly,
        systemPrompt,
        worksheetId,
        saveImageCallback
      );

      if (!svgResults || svgResults.length !== imagePrompts.length) {
        this.logger.error(`Failed to generate all images: expected ${imagePrompts.length}, got ${svgResults?.length || 0}`);
        return false;
      }

      // Final update to ensure all images are saved (this is now redundant but serves as a safety check)
       this.worksheetPromptResultModel.findOneAndUpdate(
        { worksheetId },
        {
          'promptResult.result': questions
        }
      ).exec();

      // Emit completion event
      this.socketGateway.emitWorksheetProgress(
        worksheetId,
        questions.length,
        questions.length,
        {
          imageGenerationComplete: true,
          updatedQuestions: questions,
          stage: 'complete'
        }
      );

      // Also emit a specific event for image generation completion
      this.socketGateway.server.emit('worksheet:images:complete', {
        worksheetId,
        timestamp: new Date().toISOString(),
        totalImages: imagePrompts.length,
        message: 'Image generation completed successfully'
      });

      this.logger.log(`Completed batch image processing for worksheet ${worksheetId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error in processBatchImages: ${error.message}`, error.stack);

      // Emit error event to the frontend
      if (worksheetId) {
        this.socketGateway.emitWorksheetProgress(
          worksheetId,
          0,
          0, // We don't have access to questions length here
          {
            imageGenerationError: true,
            errorMessage: error.message,
            stage: 'error'
          }
        );

        // Also emit a specific error event
        this.socketGateway.server.emit('worksheet:images:error', {
          worksheetId,
          timestamp: new Date().toISOString(),
          error: error.message
        });
      }

      return false;
    }
  }

  /**
   * Determines if image generation should be skipped for a question
   * @param imagePrompt The image prompt from the question
   * @param questionContent The content of the question
   * @returns True if image generation should be skipped, false otherwise
   */
  private shouldSkipImageGeneration(imagePrompt?: string, questionContent?: string): boolean {
    if (!imagePrompt && !questionContent) {
      return true;
    }

    // Convert HTML content if needed
    let cleanImagePrompt = imagePrompt || '';
    let cleanQuestionContent = questionContent || '';

    if (cleanImagePrompt.includes('<') && cleanImagePrompt.includes('>')) {
      cleanImagePrompt = this.convertHtmlToPlainText(cleanImagePrompt);
    }

    if (cleanQuestionContent.includes('<') && cleanQuestionContent.includes('>')) {
      cleanQuestionContent = this.convertHtmlToPlainText(cleanQuestionContent);
    }

    // Check if this is a math question
    const isMathQuestion = this.isMathQuestion(cleanQuestionContent, cleanImagePrompt);

    // Check for explicit "no image" instructions in the prompt
    if (imagePrompt) {
      const noImagePhrases = [
        'no image',
        'no diagram',
        'no illustration',
        'not required',
        'no image required',
        'no diagram required',
        'no illustration required',
        'no image needed',
        'no diagram needed',
        'no illustration needed'
      ];

      const isNoImageRequired = noImagePhrases.some(phrase =>
        imagePrompt.toLowerCase().includes(phrase)
      );

      if (isNoImageRequired) {
        this.logger.log(`Skipping image generation due to explicit "no image" instruction in prompt`);
        return true;
      }
    }

    // For math questions, check if it's a type that typically doesn't need images
    if (isMathQuestion) {
      // Check if this is a math question that doesn't need an image
      const noImageNeededForMathTypes = this.shouldSkipImageForMathQuestion(cleanQuestionContent, cleanImagePrompt);

      if (noImageNeededForMathTypes) {
        this.logger.log(`Skipping image generation for math question that doesn't need an image: ${cleanQuestionContent.substring(0, 50)}...`);
        return true;
      }

      // For other math questions, generate an image
      return false;
    }

    return false;
  }

  /**
   * Determines if a math question doesn't need an image based on its content and topic
   * @param questionContent The content of the question
   * @param imagePrompt The image prompt from the question
   * @returns True if image generation should be skipped for this math question, false otherwise
   */
  private shouldSkipImageForMathQuestion(questionContent: string, imagePrompt: string): boolean {
    const contentLower = questionContent.toLowerCase();
    const promptLower = imagePrompt.toLowerCase();

    // List of math topics that typically don't need images
    const noImageMathTopics = [
      'whole number', 'whole numbers',
      'place value', 'place values',
      'digit value', 'digit values',
      'rounding', 'round off',
      'multiplication', 'division', 'addition', 'subtraction',
      'arithmetic', 'calculation', 'compute',
      'ratio', 'proportion',
      'order of operations', 'bodmas', 'pemdas',
      'mental calculation', 'mental math',
      'number pattern', 'number sequence',
      'prime number', 'prime factorization',
      'hcf', 'lcm', 'highest common factor', 'lowest common multiple'
    ];

    // Check if the question is about a topic that doesn't need an image
    const isNoImageTopic = noImageMathTopics.some(topic =>
      contentLower.includes(topic) || promptLower.includes(topic)
    );

    if (isNoImageTopic) {
      // Double-check that the question doesn't contain keywords that would benefit from an image
      const imageNeededKeywords = [
        'diagram', 'figure', 'graph', 'chart', 'plot',
        'draw', 'sketch', 'illustrate', 'visualize',
        'shape', 'triangle', 'square', 'rectangle', 'circle', 'polygon',
        'angle', 'degree', 'coordinate', 'axis',
        'area', 'perimeter', 'volume', 'surface area',
        'map', 'scale', 'distance', 'speed', 'time',
        'fraction model', 'fraction representation',
        'number line', 'bar model'
      ];

      const needsImageAnyway = imageNeededKeywords.some(keyword =>
        contentLower.includes(keyword) || promptLower.includes(keyword)
      );

      // If it contains keywords that benefit from an image, don't skip
      if (needsImageAnyway) {
        return false;
      }

      // Check for specific question patterns that don't need images
      const noImagePatterns = [
        /what is the value of/i,
        /calculate the/i,
        /compute the/i,
        /evaluate the/i,
        /find the value of/i,
        /solve for/i,
        /which of these/i,
        /which number/i,
        /what number/i,
        /how many/i,
        /is the statement true or false/i
      ];

      const matchesNoImagePattern = noImagePatterns.some(pattern =>
        pattern.test(contentLower)
      );

      if (matchesNoImagePattern) {
        return true;
      }
    }

    return isNoImageTopic;
  }

  /**
   * Converts HTML and MathML content to plain text
   * @param content The HTML/MathML content to convert
   * @returns Plain text representation of the content
   */
  private convertHtmlToPlainText(content: string): string {
    if (!content) {
      return '';
    }

    try {
      // Create a DOM from the HTML content
      const dom = new JSDOM(`<div>${content}</div>`);
      const document = dom.window.document;

      // Process MathML elements
      const mathElements = document.querySelectorAll('math');
      mathElements.forEach(mathElement => {
        // Extract fractions
        const fractions = mathElement.querySelectorAll('mfrac');
        fractions.forEach(fraction => {
          const numerator = fraction.querySelector('mn:first-child')?.textContent || '';
          const denominator = fraction.querySelector('mn:last-child')?.textContent || '';
          // Replace the fraction with a plain text representation
          const textNode = document.createTextNode(`${numerator}/${denominator}`);
          fraction.parentNode?.replaceChild(textNode, fraction);
        });

        // Extract other math elements and convert to plain text
        const mathText = mathElement.textContent || '';
        const textNode = document.createTextNode(mathText);
        mathElement.parentNode?.replaceChild(textNode, mathElement);
      });

      // Get the plain text content
      const plainText = document.querySelector('div')?.textContent || '';

      // Clean up the text
      return plainText.trim()
        .replace(/\s+/g, ' ')  // Replace multiple spaces with a single space
        .replace(/\n+/g, ' '); // Replace newlines with spaces
    } catch (error) {
      this.logger.error(`Error converting HTML to plain text: ${error.message}`, error.stack);
      // If conversion fails, do basic cleanup
      return content
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/\s+/g, ' ')    // Replace multiple spaces with a single space
        .trim();
    }
  }

  /**
   * Determines if a question is a math question based on content and image prompt
   * @param questionContent The content of the question
   * @param imagePrompt The image prompt from the question
   * @returns True if it's a math question, false otherwise
   */
  private isMathQuestion(questionContent?: string, imagePrompt?: string): boolean {
    if (!questionContent && !imagePrompt) {
      return false;
    }

    // Content should already be converted to plain text by the time this method is called,
    // but we'll check again just to be safe
    let cleanQuestionContent = questionContent || '';
    let cleanImagePrompt = imagePrompt || '';

    if (cleanQuestionContent.includes('<') && cleanQuestionContent.includes('>')) {
      cleanQuestionContent = this.convertHtmlToPlainText(cleanQuestionContent);
    }

    if (cleanImagePrompt.includes('<') && cleanImagePrompt.includes('>')) {
      cleanImagePrompt = this.convertHtmlToPlainText(cleanImagePrompt);
    }

    // Enhanced list of math keywords, categorized for better organization
    const mathKeywords = [
      // General math terms
      'math', 'mathematics', 'equation', 'formula', 'calculation',

      // Math branches
      'algebra', 'geometry', 'trigonometry', 'calculus', 'arithmetic',

      // Geometric concepts
      'angle', 'degree', 'radian', 'triangle', 'square', 'circle', 'rectangle', 'polygon',
      'diameter', 'radius', 'circumference', 'perimeter', 'area', 'volume', 'surface area',

      // Measurement and motion
      'speed', 'distance', 'time', 'velocity', 'acceleration', 'meter', 'kilometer', 'centimeter',

      // Number types and operations
      'fraction', 'decimal', 'percentage', 'ratio', 'proportion',
      'sum', 'difference', 'product', 'quotient', 'remainder',
      'integer', 'number', 'digit', 'numerical', 'prime', 'composite', 'factor', 'multiple',

      // Data and functions
      'graph', 'function', 'coordinate', 'axis', 'plot', 'chart', 'diagram',

      // Financial terms
      'financial', 'money', 'currency', 'interest', 'principal', 'profit', 'loss',

      // Additional math concepts
      'average', 'mean', 'median', 'mode', 'range', 'probability', 'statistics',
      'parallel', 'perpendicular', 'symmetry', 'transformation', 'rotation', 'reflection',
      'sequence', 'pattern', 'series', 'inequality', 'expression'
    ];

    // Check question content
    if (cleanQuestionContent) {
      const contentLower = cleanQuestionContent.toLowerCase();

      // Check for math keywords
      if (mathKeywords.some(keyword => contentLower.includes(keyword))) {
        return true;
      }

      // Check for mathematical symbols and patterns
      const mathSymbolPatterns = [
        /\d+\s*[+\-×÷*/]\s*\d+/,  // Basic operations with numbers
        /\d+\s*=\s*\d+/,          // Equations
        /\d+\s*[<>≤≥]\s*\d+/,     // Inequalities
        /\(\s*\d+\s*[+\-×÷*/]\s*\d+\s*\)/,  // Parenthesized expressions
        /\d+\s*\/\s*\d+/,         // Fractions
        /\d+\s*%/,                // Percentages
        /\d+\s*:\s*\d+/           // Ratios
      ];

      if (mathSymbolPatterns.some(pattern => pattern.test(contentLower))) {
        return true;
      }
    }

    // Check image prompt
    if (cleanImagePrompt) {
      const promptLower = cleanImagePrompt.toLowerCase();

      // Check for math keywords
      if (mathKeywords.some(keyword => promptLower.includes(keyword))) {
        return true;
      }

      // Check for specific phrases that indicate math problems
      const mathPhrases = [
        'speed and distance problem',
        'financial problem',
        'math problem',
        'mathematical problem',
        'geometry problem',
        'algebra problem',
        'arithmetic problem',
        'calculation problem',
        'number problem',
        'word problem',
        'fraction problem',
        'percentage problem',
        'ratio problem',
        'measurement problem'
      ];

      if (mathPhrases.some(phrase => promptLower.includes(phrase))) {
        return true;
      }
    }

    return false;
  }
}
