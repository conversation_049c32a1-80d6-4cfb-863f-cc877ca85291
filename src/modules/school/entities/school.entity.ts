import { <PERSON><PERSON><PERSON>, Col<PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne } from 'typeorm';
import BaseEntity from 'src/core/entities/base-entity';
import { ApiProperty } from '@nestjs/swagger';
import { Brand } from 'src/modules/brand/entities/brand.entity';
import { User } from 'src/modules/user/entities/user.entity';

@Entity('schools')
export class School extends BaseEntity {
  @ApiProperty({
    description: 'The official name of the school',
    example: 'Springfield Elementary School',
  })
  @Column()
  name: string;

  @ApiProperty({
    description: 'The physical location of the school',
    example: '123 Education St, Springfield',
  })
  @Column()
  address: string;

  @ApiProperty({
    description: 'Contact number for the school',
    example: '******-123-4567',
  })
  @Column()
  phoneNumber: string;

  @ApiProperty({
    description: 'Official registration or license number',
    example: 'REG12345678',
  })
  @Column()
  registeredNumber: string;

  @ApiProperty({
    description: 'Official email address for communications',
    example: '<EMAIL>',
  })
  @Column()
  email: string;

  @ApiProperty({
    description: 'Reference to the school administrator (School Manager user)',
    type: () => User,
  })
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'adminId' })
  admin: User;

  @Column({ nullable: true })
  adminId: string;

  @ApiProperty({
    description: 'Reference to the associated brand information',
    type: () => Brand,
  })
  @OneToOne(() => Brand, { nullable: true })
  @JoinColumn({ name: 'brandId' })
  brand: Brand;

  @Column({ nullable: true })
  brandId: string;
}