import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { School } from './entities/school.entity';
import { NarrativeStructure } from './entities/narrative-structure.entity';
import { SchoolService } from './school.service';
import { SchoolController } from './school.controller';
import { BrandModule } from '../brand/brand.module';
import { UserModule } from '../user/user.module';
import { DocumentsModule } from '../documents/documents.module';
import { MultimodalModule } from '../multimodal/multimodal.module';
import { AiModule } from '../ai/ai.module';
import { NarrativeStructureService } from './services/narrative-structure.service';
import { NarrativeStructureExtractorService } from './services/narrative-structure-extractor.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([School, NarrativeStructure]),
    BrandModule,
    UserModule,
    DocumentsModule,
    MultimodalModule,
    AiModule,
  ],
  providers: [
    SchoolService,
    NarrativeStructureService,
    NarrativeStructureExtractorService,
  ],
  controllers: [SchoolController],
  exports: [
    SchoolService,
    NarrativeStructureService,
    NarrativeStructureExtractorService,
  ],
})
export class SchoolModule {}
