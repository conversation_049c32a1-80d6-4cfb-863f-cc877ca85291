import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { QuestionPoolService } from './question-pool.service';
import { BuildPromptService } from '../build-prompt/build-prompt.service';
import { QuestionGeneratorService } from '../prompt/services/question-generator.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OptionType } from '../options/entities/option-type.entity';
import { OptionValue } from '../options/entities/option-value.entity';

@Injectable()
export class QuestionGeneratorCronService {
  private readonly logger = new Logger(QuestionGeneratorCronService.name);
  private readonly QUESTIONS_PER_BATCH = 50;
  private isGenerating = false;

  constructor(
    private questionPoolService: QuestionPoolService,
    private buildPromptService: BuildPromptService,
    private questionGeneratorService: QuestionGeneratorService,
    @InjectRepository(OptionType)
    private optionTypeRepository: Repository<OptionType>,
    @InjectRepository(OptionValue)
    private optionValueRepository: Repository<OptionValue>,
  ) {}

  /**
   * Generate questions every 20 minutes
   */
  @Cron('0 0 */8 * * *') // Run every 8 hours
  async generateQuestions() {
    if (this.isGenerating) {
      this.logger.log('Question generation already in progress. Skipping this run.');
      return;
    }

    this.isGenerating = true;
    this.logger.log('Starting scheduled question generation...');

    try {
      this.logger.log('Fetching topics...');
      // Get all topics (main subject areas)
      const topics = await this.fetchTopics();

      // Create a default topic if none found
      if (topics.length === 0) {
        this.logger.warn('No topics found in the database, using default topic');
        const defaultTopic = { id: 'default-topic', label: 'General Knowledge', value: 'general_knowledge' };

        // Get all parent subjects for this default topic
        this.logger.log('Fetching parent subjects for default topic...');
        const parentSubjects = await this.fetchParentSubjects(defaultTopic.id);

        // Get random parent subject (fetchParentSubjects now always returns at least fallback subjects)
        const randomParentSubject = parentSubjects[Math.floor(Math.random() * parentSubjects.length)];

        // Create default grade
        this.logger.warn('Using default grade');
        const defaultGrade = { id: 'default-grade', label: 'Grade 5', value: 'grade_5' };

        // Default to English language
        const defaultLanguage = { id: 'default-language', label: 'English', value: 'english' };

        this.logger.log(`Using default values - Topic: ${defaultTopic.label}, Parent Subject: ${randomParentSubject.label}, Grade: ${defaultGrade.label}, Language: ${defaultLanguage.label}`);

        await this.processQuestionGeneration(defaultTopic, randomParentSubject, defaultGrade, defaultLanguage);
        return;
      }

      // Get random topic
      const randomTopic = topics[Math.floor(Math.random() * topics.length)];
      this.logger.log(`Selected topic: ${randomTopic.label}`);

      // Get all parent subjects for this topic
      this.logger.log(`Fetching parent subjects for topic: ${randomTopic.label}...`);
      const parentSubjects = await this.fetchParentSubjects(randomTopic.id);

      // fetchParentSubjects now always returns at least fallback subjects, so this check is just for logging
      if (parentSubjects.length === 0) {
        this.logger.warn(`No parent subjects found for topic: ${randomTopic.label}, but fallbacks should be provided`);
      }

      // Get random parent subject
      const randomParentSubject = parentSubjects[Math.floor(Math.random() * parentSubjects.length)];
      this.logger.log(`Selected parent subject: ${randomParentSubject.label}`);

      // Get all grades
      this.logger.log('Fetching grades...');
      const grades = await this.fetchGrades();

      // Create a default grade if none found
      let randomGrade;
      if (grades.length === 0) {
        this.logger.warn('No grades found in the database, using default grade');
        randomGrade = { id: 'default-grade', label: 'Grade 5', value: 'grade_5' };
      } else {
        // Get random grade
        randomGrade = grades[Math.floor(Math.random() * grades.length)];
      }
      this.logger.log(`Selected grade: ${randomGrade.label}`);

      // Get all languages
      this.logger.log('Fetching languages...');
      const languages = await this.fetchLanguages();

      // Default to English if no languages found
      const randomLanguage = languages.length > 0
        ? languages[Math.floor(Math.random() * languages.length)]
        : { id: 'default-language', label: 'English', value: 'english' };
      this.logger.log(`Selected language: ${randomLanguage.label}`);

      await this.processQuestionGeneration(randomTopic, randomParentSubject, randomGrade, randomLanguage);
    } catch (error) {
      this.logger.error(`Error generating questions: ${error.message}`, error.stack);
    } finally {
      this.isGenerating = false;
    }
  }

  /**
   * Process question generation with given parameters
   * This method handles the actual question generation logic
   */
  private async processQuestionGeneration(
    topic: { id: string; label: string; value: string },
    parentSubject: { id: string; label: string; value: string },
    grade: { id: string; label: string; value: string },
    language: { id: string; label: string; value: string }
  ): Promise<void> {
    try {
      this.logger.log(`Generating questions for ${topic.label} (${parentSubject.label}) - Grade ${grade.label} in ${language.label}`);

      // Prepare user request
      const userRequest = {
        topic: topic.label,
        parentSubject: parentSubject.label,
        grade: grade.label,
        language: language.label,
        totalQuestions: this.QUESTIONS_PER_BATCH,
        difficulty: 'Medium',
        exerciseType: ['Multiple Choice', 'Fill in the Blank', 'Creative Writing', 'Single Choice', 'Open Ended', 'Matching'],
        includeImages: false
      };

      // Generate context content
      const contextContent = '';  // This will be fetched from vector DB by the question generator

      // Build prompts
      this.logger.log('Building prompts...');
      const metadataString = this.formatMetadata(userRequest);

      let systemPrompt;
      try {
        systemPrompt = this.buildPromptService.buildSystemPrompt(
          contextContent,
          metadataString,
          userRequest,
        );
      } catch (error) {
        this.logger.error(`Error building system prompt: ${error.message}`, error.stack);
        // Use a default system prompt as fallback
        systemPrompt = `Generate ${this.QUESTIONS_PER_BATCH} educational questions for ${topic.label} (${parentSubject.label}) at Grade ${grade.label} level in ${language.label}.`;
      }

      let userPrompt;
      try {
        userPrompt = this.buildPromptService.buildUserPrompt(userRequest, metadataString);
      } catch (error) {
        this.logger.error(`Error building user prompt: ${error.message}`, error.stack);
        // Use a default user prompt as fallback
        userPrompt = `Please create ${this.QUESTIONS_PER_BATCH} educational questions for ${topic.label} (${parentSubject.label}) at Grade ${grade.label} level in ${language.label}.`;
      }

      // Generate questions
      this.logger.log('Generating questions...');
      let result;
      try {
        result = await this.questionGeneratorService.generateQuestions(
          systemPrompt,
          userPrompt,
          userRequest,
          true,
        );
      } catch (error) {
        this.logger.error(`Error generating questions: ${error.message}`, error.stack);
        return;
      }

      if (result && Array.isArray(result.result) && result.result.length > 0) {
        // Add subject, grade, and language information to each question
        const questionsWithSubject = result.result.map(question => ({
          ...question,
          subject: topic.label,
          parentSubject: parentSubject.label,
          grade: grade.label,
          language: language.label,
        }));

        // Save to question pool
        this.logger.log(`Saving ${questionsWithSubject.length} questions to the pool...`);
        try {
          const savedQuestions = await this.questionPoolService.addQuestions(
            questionsWithSubject,
            { id: parentSubject.id, value: parentSubject.value },
          );
          this.logger.log(`Successfully generated and saved ${savedQuestions.length} questions to the pool`);
        } catch (error) {
          this.logger.error(`Error saving questions to pool: ${error.message}`, error.stack);
        }
      } else {
        this.logger.warn('No questions were generated or invalid result format');
      }
    } catch (error) {
      this.logger.error(`Error in processQuestionGeneration: ${error.message}`, error.stack);
    }
  }

  /**
   * Format metadata for prompt building
   */
  private formatMetadata(userRequest: any): string {
    const metadata: string[] = [];
    if (userRequest.topic) metadata.push(`Topic: ${userRequest.topic}`);
    if (userRequest.parentSubject) metadata.push(`Parent Subject: ${userRequest.parentSubject}`);
    if (userRequest.subject) metadata.push(`Subject: ${userRequest.subject}`);
    if (userRequest.grade) metadata.push(`Grade: ${userRequest.grade}`);
    if (userRequest.language) metadata.push(`Language: ${userRequest.language}`);
    if (userRequest.totalQuestions) metadata.push(`Total Questions: ${userRequest.totalQuestions}`);

    return metadata.join('\n');
  }

  /**
   * Fetch all topics (subject types) from the database
   */
  private async fetchTopics(): Promise<{ id: string; label: string; value: string }[]> {
    try {
      // Try multiple possible keys for topic type
      const possibleKeys = ['topic', 'subject', 'subject_area', 'main_subject'];
      let topicType: OptionType | null = null;

      for (const key of possibleKeys) {
        topicType = await this.optionTypeRepository.findOne({
          where: { key },
        });

        if (topicType) {
          this.logger.log(`Found topic type with key: ${key}`);
          break;
        }
      }

      if (!topicType) {
        this.logger.error('Topic option type not found with any of the expected keys');

        // Create default fallback topics when none are found
        this.logger.warn('Using fallback topics');
        return [
          { id: 'fallback-topic-1', label: 'Mathematics', value: 'mathematics' },
          { id: 'fallback-topic-2', label: 'English', value: 'english' },
          { id: 'fallback-topic-3', label: 'Science', value: 'science' }
        ];
      }

      const topics = await this.optionValueRepository.find({
        where: { optionTypeId: topicType.id },
      });

      // If no topics found, provide fallback options
      if (topics.length === 0) {
        this.logger.warn('No topics found for the topic type, using fallback topics');
        return [
          { id: 'fallback-topic-1', label: 'Mathematics', value: 'mathematics' },
          { id: 'fallback-topic-2', label: 'English', value: 'english' },
          { id: 'fallback-topic-3', label: 'Science', value: 'science' }
        ];
      }

      return topics.map(topic => ({
        id: topic.id,
        label: topic.label,
        value: topic.value,
      }));
    } catch (error) {
      this.logger.error(`Error fetching topics: ${error.message}`, error.stack);

      // Return fallback topics even in case of error
      this.logger.warn('Using fallback topics due to error');
      return [
        { id: 'fallback-topic-1', label: 'Mathematics', value: 'mathematics' },
        { id: 'fallback-topic-2', label: 'English', value: 'english' },
        { id: 'fallback-topic-3', label: 'Science', value: 'science' }
      ];
    }
  }

  /**
   * Fetch parent subjects for a given topic
   */
  private async fetchParentSubjects(topicId: string): Promise<{ id: string; label: string; value: string }[]> {
    try {
      // Try multiple possible keys for subject type
      const possibleKeys = ['subject_type', 'subject', 'parent_subject', 'parent_topic'];
      let subjectType: OptionType | null = null;

      for (const key of possibleKeys) {
        subjectType = await this.optionTypeRepository.findOne({
          where: { key },
        });

        if (subjectType) {
          this.logger.log(`Found subject type with key: ${key}`);
          break;
        }
      }

      if (!subjectType) {
        this.logger.error('Subject Type option type not found with any of the expected keys');

        // Create default fallback subjects when none are found
        this.logger.warn('Using fallback parent subjects');
        return [
          { id: 'fallback-1', label: 'General', value: 'general' },
          { id: 'fallback-2', label: 'Fundamentals', value: 'fundamentals' },
          { id: 'fallback-3', label: 'Basic Concepts', value: 'basic_concepts' }
        ];
      }

      // In a real implementation, you would filter by topic ID
      // For now, we'll just return all subject types as parent subjects
      // This should be enhanced to properly filter by topic when the data structure allows
      const parentSubjects = await this.optionValueRepository.find({
        where: { optionTypeId: subjectType.id },
      });

      this.logger.log(`Found ${parentSubjects.length} parent subjects for topic ID: ${topicId}`);

      // If no parent subjects found, provide fallback options
      if (parentSubjects.length === 0) {
        this.logger.warn(`No parent subjects found for topic ID: ${topicId}, using fallback subjects`);
        return [
          { id: 'fallback-1', label: 'General', value: 'general' },
          { id: 'fallback-2', label: 'Fundamentals', value: 'fundamentals' },
          { id: 'fallback-3', label: 'Basic Concepts', value: 'basic_concepts' }
        ];
      }

      return parentSubjects.map(subject => ({
        id: subject.id,
        label: subject.label,
        value: subject.value,
      }));
    } catch (error) {
      this.logger.error(`Error fetching parent subjects: ${error.message}`, error.stack);

      // Return fallback subjects even in case of error
      this.logger.warn('Using fallback parent subjects due to error');
      return [
        { id: 'fallback-1', label: 'General', value: 'general' },
        { id: 'fallback-2', label: 'Fundamentals', value: 'fundamentals' },
        { id: 'fallback-3', label: 'Basic Concepts', value: 'basic_concepts' }
      ];
    }
  }

  /**
   * Fetch all grades from the database
   */
  private async fetchGrades(): Promise<{ id: string; label: string; value: string }[]> {
    try {
      // Try multiple possible keys for grade type
      const possibleKeys = ['grade', 'level', 'year', 'class'];
      let gradeType: OptionType | null = null;

      for (const key of possibleKeys) {
        gradeType = await this.optionTypeRepository.findOne({
          where: { key },
        });

        if (gradeType) {
          this.logger.log(`Found grade type with key: ${key}`);
          break;
        }
      }

      if (!gradeType) {
        this.logger.error('Grade option type not found with any of the expected keys');

        // Create default fallback grades when none are found
        this.logger.warn('Using fallback grades');
        return [
          { id: 'fallback-grade-1', label: 'Grade 1', value: 'grade_1' },
          { id: 'fallback-grade-2', label: 'Grade 2', value: 'grade_2' },
          { id: 'fallback-grade-3', label: 'Grade 3', value: 'grade_3' },
          { id: 'fallback-grade-4', label: 'Grade 4', value: 'grade_4' },
          { id: 'fallback-grade-5', label: 'Grade 5', value: 'grade_5' },
          { id: 'fallback-grade-6', label: 'Grade 6', value: 'grade_6' }
        ];
      }

      const grades = await this.optionValueRepository.find({
        where: { optionTypeId: gradeType.id },
      });

      // If no grades found, provide fallback options
      if (grades.length === 0) {
        this.logger.warn('No grades found for the grade type, using fallback grades');
        return [
          { id: 'fallback-grade-1', label: 'Grade 1', value: 'grade_1' },
          { id: 'fallback-grade-2', label: 'Grade 2', value: 'grade_2' },
          { id: 'fallback-grade-3', label: 'Grade 3', value: 'grade_3' },
          { id: 'fallback-grade-4', label: 'Grade 4', value: 'grade_4' },
          { id: 'fallback-grade-5', label: 'Grade 5', value: 'grade_5' },
          { id: 'fallback-grade-6', label: 'Grade 6', value: 'grade_6' }
        ];
      }

      return grades.map(grade => ({
        id: grade.id,
        label: grade.label,
        value: grade.value,
      }));
    } catch (error) {
      this.logger.error(`Error fetching grades: ${error.message}`, error.stack);

      // Return fallback grades even in case of error
      this.logger.warn('Using fallback grades due to error');
      return [
        { id: 'fallback-grade-1', label: 'Grade 1', value: 'grade_1' },
        { id: 'fallback-grade-2', label: 'Grade 2', value: 'grade_2' },
        { id: 'fallback-grade-3', label: 'Grade 3', value: 'grade_3' },
        { id: 'fallback-grade-4', label: 'Grade 4', value: 'grade_4' },
        { id: 'fallback-grade-5', label: 'Grade 5', value: 'grade_5' },
        { id: 'fallback-grade-6', label: 'Grade 6', value: 'grade_6' }
      ];
    }
  }

  /**
   * Fetch all languages from the database
   */
  private async fetchLanguages(): Promise<{ id: string; label: string; value: string }[]> {
    try {
      // Try multiple possible keys for language type
      const possibleKeys = ['language', 'lang', 'locale', 'instruction_language'];
      let languageType: OptionType | null = null;

      for (const key of possibleKeys) {
        languageType = await this.optionTypeRepository.findOne({
          where: { key },
        });

        if (languageType) {
          this.logger.log(`Found language type with key: ${key}`);
          break;
        }
      }

      if (!languageType) {
        this.logger.error('Language option type not found with any of the expected keys');

        // Create default fallback languages when none are found
        this.logger.warn('Using fallback languages');
        return [
          { id: 'fallback-lang-1', label: 'English', value: 'english' },
          { id: 'fallback-lang-2', label: 'Chinese', value: 'chinese' },
          { id: 'fallback-lang-3', label: 'Malay', value: 'malay' },
          { id: 'fallback-lang-4', label: 'Tamil', value: 'tamil' }
        ];
      }

      const languages = await this.optionValueRepository.find({
        where: { optionTypeId: languageType.id },
      });

      // If no languages found, provide fallback options
      if (languages.length === 0) {
        this.logger.warn('No languages found for the language type, using fallback languages');
        return [
          { id: 'fallback-lang-1', label: 'English', value: 'english' },
          { id: 'fallback-lang-2', label: 'Chinese', value: 'chinese' },
          { id: 'fallback-lang-3', label: 'Malay', value: 'malay' },
          { id: 'fallback-lang-4', label: 'Tamil', value: 'tamil' }
        ];
      }

      return languages.map(language => ({
        id: language.id,
        label: language.label,
        value: language.value,
      }));
    } catch (error) {
      this.logger.error(`Error fetching languages: ${error.message}`, error.stack);

      // Return fallback languages even in case of error
      this.logger.warn('Using fallback languages due to error');
      return [
        { id: 'fallback-lang-1', label: 'English', value: 'english' },
        { id: 'fallback-lang-2', label: 'Chinese', value: 'chinese' },
        { id: 'fallback-lang-3', label: 'Malay', value: 'malay' },
        { id: 'fallback-lang-4', label: 'Tamil', value: 'tamil' }
      ];
    }
  }
}
