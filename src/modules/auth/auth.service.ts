import {forwardRef, Inject, Injectable, UnauthorizedException} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UserService } from '../user/user.service';
import { SchoolService } from '../school/school.service';
import { SignInDto } from './dto/sign-in.dto';
import { SignUpDto } from './dto/sign-up';
import { EUserRole } from '../user/dto/create-user.dto';
import { Bcrypt } from 'src/core/utils/bcrypt';

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
    @Inject(forwardRef(() => SchoolService))
    private readonly schoolService: SchoolService,
  ) {}

  async signIn(loginDto: SignInDto) {
    const user = await this.userService.findByEmail(loginDto.email);
    if (!user) {
      console.log('User not found'); // Debug log
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await Bcrypt.compare(
      loginDto.password,
      user.password,
    );

    if (!isPasswordValid) {
      // Ensure hashing is correct
      throw new UnauthorizedException('Invalid credentials');
    }

    // Include school information in the token if available
    const payload = { 
      sub: user.id, 
      email: user.email, 
      role: user.role,
      schoolId: user.schoolId || null
    };

    // Prepare user response object
    const userResponse = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      schoolId: user.schoolId || null
    };

    // Fetch school information if user has a schoolId
    if (user.schoolId) {
      try {
        const school = await this.schoolService.findOne(user.schoolId);
        // Add school information to the response
        userResponse['school'] = school;
      } catch (error) {
        // If school not found, just continue without adding school info
        console.log(`School with ID ${user.schoolId} not found`);
      }
    }

    return { 
      accessToken: this.jwtService.sign(payload),
      user: userResponse
    };
  }

  async signUp(registerDto: SignUpDto) {
    const { role = EUserRole.TEACHER, schoolId, ...userData } = registerDto;

    // Validate role and school combinations
    if (role === EUserRole.INDEPENDENT_TEACHER) {
      // INDEPENDENT_TEACHER must not have a schoolId
      if (schoolId) {
        throw new Error('Independent Teacher cannot be assigned to an existing school');
      }
    } else if ((role === EUserRole.TEACHER || role === EUserRole.STUDENT || role === EUserRole.SCHOOL_MANAGER) && !schoolId) {
      throw new Error(`School ID is required for ${role} role`);
    }

    // Create user with specified role and school
    return this.userService.create({
      ...userData,
      role,
      schoolId: role === EUserRole.INDEPENDENT_TEACHER ? null : schoolId,
    });
  }
}
