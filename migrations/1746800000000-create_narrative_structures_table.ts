import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateNarrativeStructuresTable1746800000000 implements MigrationInterface {
    name = 'CreateNarrativeStructuresTable1746800000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "narrative_structures" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                "deletedAt" TIMESTAMP,
                "schoolId" uuid NOT NULL,
                "content" text NOT NULL,
                "sourceFormatsCount" integer NOT NULL DEFAULT '0',
                "extractedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                "version" integer NOT NULL DEFAULT '1',
                "extractionMetadata" jsonb,
                CONSTRAINT "PK_narrative_structures" PRIMARY KEY ("id"),
                CONSTRAINT "UQ_narrative_structures_school" UNIQUE ("schoolId")
            )
        `);

        await queryRunner.query(`
            ALTER TABLE "narrative_structures" 
            ADD CONSTRAINT "FK_narrative_structures_school" 
            FOREIGN KEY ("schoolId") REFERENCES "schools"("id") 
            ON DELETE CASCADE ON UPDATE NO ACTION
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_narrative_structures_school_id" 
            ON "narrative_structures" ("schoolId")
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_narrative_structures_extracted_at" 
            ON "narrative_structures" ("extractedAt")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "IDX_narrative_structures_extracted_at"`);
        await queryRunner.query(`DROP INDEX "IDX_narrative_structures_school_id"`);
        await queryRunner.query(`ALTER TABLE "narrative_structures" DROP CONSTRAINT "FK_narrative_structures_school"`);
        await queryRunner.query(`DROP TABLE "narrative_structures"`);
    }
}
