# NestJS
NODE_ENV=development
NESTJS_PORT=4000

JWT_SECRET=e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
JWT_EXPIRATION=30d

# PostgreSQL
DB_USER=postgres
DB_PASSWORD=Password5%
DB_NAME=edusg_db
DB_PORT=54321
DB_HOST=localhost
DB_USERNAME=user

# Redis
REDIS_PORT=63791

# Qdrant
QDRANT_COLLECTION=edusg
QDRANT_URL=http://localhost
QDRANT_PORT=63331
QDRANT_GRPC_PORT=63341
QDRANT_ALLOW_CORS=true
VECTOR_SIZE=1536

# Pinecone
PINECONE_API_KEY=pcsk_3Jytjt_2gq3ULH2NEks2dHbGSkgneKLEURighN2CycfcNejLhJexc3WekKMg1qJ9dFSZUp
PINECONE_NAME_SPACE=edusg

VECTOR_DB_TYPE=pinecone

# OpenAI
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4.1-nano

# AI Model Configuration
# Task-specific models (will fallback to OPENAI_MODEL if not set)
NARRATIVE_STRUCTURE_MODEL=gpt-4.1-nano
QUESTION_GENERATION_MODEL=gpt-4.1-nano
IMAGE_GENERATION_MODEL=gpt-4.1-nano
MULTIMODAL_MODEL=qwen/qwen2.5-vl-72b-instruct

GOOGLE_API_KEY=AIzaSyAqqThSFB_Oz42w8c3U1aKXtasxeOqHQ34

MONGO_INITDB_ROOT_USERNAME=root
MONGO_INITDB_ROOT_PASSWORD=password
MONGODB_URI=**************************************************************

REDIS_PASSWORD=redis
REDIS_COMMON=redis://:redis@127.0.0.1:6379

USE_PARALLEL_PROCESSING=true
PARALLEL_BATCHES=2
PARALLEL_THRESHOLD=2

# Question Pool Configuration
QUESTION_POOL_ENABLED=true
DEFAULT_SELECTION_STRATEGY=hybrid
MIN_POOL_QUESTIONS_THRESHOLD=10

# Question Pool Feature Flags
ALLOW_POOL_ONLY_STRATEGY=true
ALLOW_AI_ONLY_STRATEGY=true
ALLOW_HYBRID_STRATEGY=true
ALLOW_MIXED_STRATEGY=true

# Multimodal Processing
# Note: Requires poppler-utils to be installed on the system (apt-get install poppler-utils or brew install poppler)
USE_MULTIMODAL_PROCESSING=true
IMAGE_STORAGE_PATH=./uploads/images
OPENROUTER_API_KEY=sk-or-v1-